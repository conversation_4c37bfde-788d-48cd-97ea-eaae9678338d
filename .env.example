# Cloudflare Turnstile Configuration
# 获取方式：https://dash.cloudflare.com/profile/api-tokens
# 1. 登录 Cloudflare Dashboard
# 2. 进入 Turnstile 页面
# 3. 创建新的站点
# 4. 获取 Site Key 和 Secret Key

# Turnstile Site Key (公开的，用于前端)
TURNSTILE_SITE_KEY=0x4AAAAAABof2Gg619dti5bh

# Turnstile Secret Key (私密的，用于后端验证)
TURNSTILE_SECRET_KEY=0x4AAAAAABof2HpFMJKo_AzuaoH9BZOZeiQ

# 注意：
# - Site Key 已经在 HTML 中硬编码，如需更改请同时修改 index.html
# - Secret Key 需要在 Supabase 项目设置中配置为环境变量
# - 本地开发时，请将此文件复制为 .env 并填入真实的密钥
