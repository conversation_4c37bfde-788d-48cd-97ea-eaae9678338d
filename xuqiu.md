# F1 TV M3U 播放网站 - 产品需求文档 (PRD)

## 1. 项目概述

### 1.1. 产品目标
创建一个简洁、高效、轻量级的 F1 赛事 M3U 流媒体播放网站。受 MPV 播放器启发，本站旨在提供一个**集视频播放、社区互动和赛事信息于一体**的综合性平台，同时保持以视频为核心的无干扰观看体验。

### 1.2. 目标用户
- 希望通过 M3U 链接观看 F1 赛事的爱好者。
- 喜欢在观看比赛时进行实时讨论和互动的用户。
- 希望一站式获取赛程和积分榜等核心信息的 F1 车迷。
- 偏好简约、无广告、高性能工具的技术爱好者。

### 1.3. 设计哲学
- **简约主义**: 界面上只保留最核心的功能，避免任何不必要的视觉元素和功能干扰。
- **性能优先**: 网站加载速度快，视频播放流畅，弹幕系统实时性高。
- **键盘友好**: 提供常用的播放控制快捷键，提升重度用户的使用体验。
- **社区驱动**: 鼓励用户贡献和分享有效的播放源，共同维护社区。

---

## 2. 核心功能 (MVP + 扩展)

### 2.1. 视频播放核心
- **播放源**:
    - **社区精选源**: 播放器默认加载经管理员审核通过的、由社区用户贡献的 M3U/M3U8 源。用户可以通过一个下拉菜单在不同线路或频道间轻松切换。
    - **用户自定义源**: 保留页面顶部或中部的输入框，供用户粘贴自己的 M3U/M3U8 直播流地址进行播放。这为用户提供了最大的灵活性。
    - **体验优化**: 网站应使用 `localStorage` 自动保存用户上次使用的播放源（无论是社区源还是自定义源），下次访问时自动加载。
- **播放器**:
    - 使用 `hls.js` 等成熟的库来处理 HLS (M3U/M3U8) 流。
    - **基础控件**: 只提供最必要的播放器控件：播放/暂停、音量调节、全屏。
    - **键盘快捷键**:
        - `空格键`: 播放 / 暂停
        - `F 键` 或 `双击播放器`: 进入 / 退出全屏
        - `上/下方向键`: 增加 / 减小音量
        - `左/右方向键`: 小幅度快退 / 快进

### 2.2. 弹幕系统 (无变更)
- **弹幕显示**:
    - 弹幕从视频区域右侧滚动到左侧，半透明背景，避免过度遮挡画面。
    - 弹幕密度过高时，应有策略避免重叠（例如，分层渲染）。
- **弹幕发送**:
    - 播放器下方提供一个简单的文本输入框和“发送”按钮。
    - 用户（无论是否登录）都可以发送弹幕。
    - **发送频率限制**: 为防止刷屏，后端应对同一用户（或同一 IP 的匿名用户）的弹幕发送设置频率限制（如：3-5秒/条）。
- **数据存储**:
    - 所有弹幕内容存储在 Supabase 的一张 `danmaku` 表中。

### 2.3. 用户系统 (无变更)
- **注册/登录**:
    - 用户使用 **邮箱** 和 **密码** 进行注册和登录。
    - 登录状态应持久化。
- **用户状态显示**:
    - **未登录**: 页面右上角显示“登录/注册”按钮。
    - **已登录**: 页面右上角显示用户邮箱或一个简约的头像/图标，并提供“登出”选项。

### 2.4. 社区播放源贡献系统 (新增)
- **播放源提交**:
    - 登录用户可以通过一个专门的表单页面或弹窗来提交新的 M3U/M3U8 播放源地址。
    - 提交时可附带简短的描述（如“线路1”、“720P”等）。
- **管理员审核**:
    - 用户提交的源默认状态为“待审核”，不会公开显示。
    - 需要一个简单的管理后台（或直接在 Supabase 后台操作），让管理员可以审核、批准或拒绝这些提交。
    - 只有“已批准”的源才会出现在公共的播放源选择列表中。
- **数据存储**:
    - 在 Supabase 中创建一张 `streams` 表，用于存储用户贡献的播放源。

### 2.5. F1 赛程与积分榜 (新增)
- **信息展示**:
    - 在网站的非核心区域（如一个可切换的侧边栏或一个独立的“信息”页面/弹窗）提供 F1 相关信息。
- **赛程 (Schedule)**:
    - 以列表形式展示全年的分站赛信息，包括比赛名称、地点、日期和正赛开始时间。
    - **体验优化**: 自动高亮显示当前或下一场比赛。
- **积分榜 (Standings)**:
    - 提供两个独立的榜单：**车手积分榜** 和 **车队积分榜**。
    - 榜单内容包括排名、车手/车队名称、积分等关键信息。
- **数据来源**:
    - **MVP 阶段**: 数据可以由管理员手动录入到 Supabase 的专用表中。
    - **进阶阶段**: 考虑集成一个可靠的 F1 数据 API (如 Ergast API 或其他替代品) 来自动更新赛程和积分数据。

---

## 3. 页面设计
采用单页应用 (SPA) 模式，所有功能都承载于一个页面内。

- **Header (页头)**:
    - 左侧: 网站标题，例如 “F1 Live Stream”。
    - **新增**: 增加“赛程/积分榜”的导航入口。
    - 右侧: 用户系统组件（“登录/注册” 或 “用户名/登出”）。
- **Main (主内容区)**:
    - **播放源选择区**:
        - **社区源下拉菜单**: 位于播放器上方或侧方，用于切换官方认可的播放线路。
        - **自定义源输入框**: 默认可折叠或以一个按钮形式存在，点击后展开，供用户输入自己的链接。
    - **视频播放器**: 占据页面的主要空间。
    - **新增**: “贡献播放源”的按钮/链接，登录后可见。
- **Footer (页脚/输入区)**:
    - **弹幕输入框**: 位于播放器正下方，方便用户观看时发送。
    - 包含一个指向 GitHub 仓库的链接和 Cloudflare Pages 的部署声明。

---

## 4. 技术选型与实现思路

- **前端**:
    - **框架**: 原生 HTML5, CSS3, JavaScript (Vanilla JS)。
    - **视频处理**: `hls.js`。
    - **UI/UX**: 手写 CSS，暗色主题。
- **后端与数据**:
    - **服务**: Supabase。
    - **认证 (Auth)**: Supabase Auth。
    - **数据库 (Database)**:
        - `danmaku`: 存储弹幕。
        - `streams`: (新增) 存储用户贡献的播放源，包含 `url`, `name`, `status` ('pending', 'approved', 'rejected'), `submitter_id` 等字段。
        - `schedule`: (新增) 存储赛程信息。
        - `driver_standings`, `constructor_standings`: (新增) 存储车手和车队积分。
    - **实时 (Realtime)**: Supabase Realtime 用于弹幕系统。
    - **管理角色**: 利用 Supabase 的行级安全策略 (RLS) 或自定义逻辑来区分普通用户和管理员角色。

- **部署**:
    - **代码托管**: GitHub。
    - **网站托管**: Cloudflare Pages。

---

## 5. 开发步骤建议

1.  **阶段一：基础播放功能** (不变)
    - 创建基础 HTML 结构，集成 `hls.js`，实现自定义 M3U8 链接的播放和基础键盘快捷键。
2.  **阶段二：集成 Supabase 用户系统** (不变)
    - 集成 `supabase-js`，开发用户注册、登录、登出功能。
3.  **阶段三：开发弹幕系统** (不变)
    - 创建 `danmaku` 表，实现弹幕的发送和实时显示。
4.  **阶段四：社区播放源系统** (新增)
    - 创建 `streams` 表。
    - 开发前端的播放源提交表单。
    - 实现管理员审核流程（可在 Supabase 后台手动操作）。
    - 在播放器旁添加社区源的下拉选择菜单。
5.  **阶段五：信息中心** (新增)
    - 创建赛程和积分榜所需的数据表，并手动录入初始数据。
    - 开发前端页面/组件来展示这些信息。
6.  **阶段六：UI/UX 优化与部署** (原阶段四)
    - 完善整体 CSS 样式，优化交互体验。
    - 将代码推送到 GitHub，并配置 Cloudflare Pages 进行部署。
