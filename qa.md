# 用户删除失败的原因是外键约束：

invitation_codes 表中的 used_by_user_id 字段引用了 auth.users 表中的用户 ID
当你尝试删除用户时，PostgreSQL 检测到该用户 ID 仍被 invitation_codes 表引用
外键约束阻止了用户的删除操作
解决方案：
方案1：删除用户前先清理引用（推荐）
在 Supabase Dashboard 中：

先进入 invitation_codes 表
找到 used_by_user_id 字段等于要删除用户 ID 的记录
将这些记录的 used_by_user_id 设置为 NULL
然后再删除用户

# 设置管理员账号：
sql命令使用 jsonb 合并操作符

UPDATE auth.users 
SET raw_user_meta_data = raw_user_meta_data || '{"role": "admin"}'::jsonb
WHERE email = '<EMAIL>';