<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>F1 Live Stream</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
</head>
<body>
    <header>
        <div class="logo">F1 Live Stream</div>
        <nav>
            <a href="#" id="schedule-btn">赛程</a>
            <a href="#" id="standings-btn">积分榜</a>
        </nav>
        <div class="user-auth">
            <button id="login-btn">登录/注册</button>
            <div id="user-info" style="display: none;">
                <div class="user-avatar-container">
                    <div class="user-avatar" id="user-avatar">
                        <span class="avatar-text" id="avatar-text">U</span>
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <div class="dropdown-header">
                            <span id="dropdown-email"></span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <button class="dropdown-item" id="filter-sources-btn">
                            <span class="dropdown-icon">🔍</span>
                            发现视频源
                        </button>
                        <button class="dropdown-item" id="contribute-btn">
                            <span class="dropdown-icon">📤</span>
                            提交视频源
                        </button>
                        <div class="dropdown-divider"></div>
                        <button class="dropdown-item" id="change-password-btn">
                            <span class="dropdown-icon">🔑</span>
                            修改密码
                        </button>
                        <button class="dropdown-item" id="admin-config-btn" style="display: none;">
                            <span class="dropdown-icon">⚙️</span>
                            系统配置
                        </button>
                        <button class="dropdown-item" id="logout-btn">
                            <span class="dropdown-icon">🚪</span>
                            登出
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main>
        <!-- 视频播放区域和聊天室 -->
        <div class="video-chat-container">
            <div class="video-section">
                <div class="video-container">
                    <video id="video-player" controls></video>
                    <div class="danmaku-overlay"></div>
                </div>
                <!-- 弹幕发送区域（紧跟视频下方） -->
                <div class="danmaku-sender">
                    <input type="text" id="danmaku-input" placeholder="发个弹幕见证历史...">
                    <button id="send-danmaku-btn">发送</button>
                </div>
            </div>

            <!-- 聊天室区域 -->
            <div class="chat-section" id="chat-section">
                <div class="chat-header">
                    <span class="chat-title">聊天室</span>
                    <button class="chat-close-btn" id="chat-close-btn">
                        <span>×</span>
                    </button>
                </div>
                <div class="chat-content" id="chat-content">
                    <div class="chat-messages" id="chat-messages">
                        <!-- 聊天消息将在这里显示 -->
                    </div>
                    <div class="chat-input-container">
                        <input type="text" id="chat-input" placeholder="说点什么...">
                        <button id="send-chat-btn">发送</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频源选择区域 -->
        <div class="source-container">
            <!-- 未登录用户的手动输入区域 -->
            <div class="manual-input-section" id="manual-input-section">
                <h3>手动输入视频源</h3>
                <div class="stream-selector">
                    <input type="text" id="m3u-input" placeholder="粘贴你的 M3U/M3U8 链接...">
                    <button id="load-stream-btn">加载</button>
                </div>
            </div>

            <!-- 登录用户的频道和视频源列表 -->
            <div class="channel-sources-section" id="channel-sources-section" style="display: none;">
                <div class="source-tabs">
                    <button class="source-tab active" id="channel-tab">频道列表</button>
                    <button class="source-tab" id="manual-tab">手动输入</button>
                </div>

                <!-- 频道列表内容 -->
                <div class="tab-content active" id="channel-content">
                    <div class="channels-container">
                        <div class="channel-tabs" id="channel-tabs">
                            <!-- 频道标签将在这里显示 -->
                        </div>
                        <div class="sources-list" id="sources-list">
                            <!-- 视频源列表将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 手动输入内容 -->
                <div class="tab-content" id="manual-content">
                    <div class="stream-selector">
                        <input type="text" id="m3u-input-logged" placeholder="粘贴你的 M3U/M3U8 链接...">
                        <button id="load-stream-btn-logged">加载</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>Powered by <a href="https://github.com" target="_blank">GitHub</a> & <a href="https://pages.cloudflare.com/" target="_blank">Cloudflare Pages</a>. MPV-style F1 Viewer.</p>
    </footer>

    <!-- Schedule Modal -->
    <div id="schedule-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <button class="auth-close-btn">&times;</button>
            <h2>F1 赛程</h2>
            <div id="schedule-content"></div>
        </div>
    </div>

    <!-- Standings Modal -->
    <div id="standings-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <button class="auth-close-btn">&times;</button>
            <h2>F1 积分榜</h2>
            <div class="modal-tabs">
                <button class="tab-link active" data-tab="driver-standings">车手积分榜</button>
                <button class="tab-link" data-tab="constructor-standings">车队积分榜</button>
            </div>
            <div id="driver-standings" class="tab-content active">
                <h3>车手积分榜 (Driver Standings)</h3>
                <div id="driver-standings-content"></div>
            </div>
            <div id="constructor-standings" class="tab-content">
                <h3>车队积分榜 (Constructor Standings)</h3>
                <div id="constructor-standings-content"></div>
            </div>
        </div>
    </div>

    <!-- Step 1: Discovery Modal (发现筛选) -->
    <div id="discovery-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content large-modal">
            <button class="auth-close-btn">&times;</button>
            <div class="step-header">
                <h2>🔍 发现视频源</h2>
                <p class="step-description">从大仓库中筛选和发现适合的视频源</p>
                <div class="step-indicator">
                    <span class="step active">1. 发现筛选</span>
                    <span class="step-arrow">→</span>
                    <span class="step">2. 个人管理</span>
                </div>
            </div>

            <!-- 筛选区 -->
            <div class="discovery-filters">
                <div class="filter-group">
                    <div class="precise-filters">
                        <input type="text" id="search-streams" placeholder="搜索关键词...">
                        <select id="channel-filter">
                            <option value="">所有频道</option>
                            <option value="CCTV5">CCTV5</option>
                            <option value="五星体育">五星体育</option>
                            <option value="广东体育">广东体育</option>
                            <option value="其他">其他</option>
                        </select>
                        <select id="quality-filter">
                            <option value="">所有画质</option>
                            <option value="4K">4K</option>
                            <option value="1080p">1080p</option>
                            <option value="720p">720p</option>
                            <option value="480p">480p</option>
                        </select>
                        <button id="search-btn">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 发现结果 -->
            <div class="discovery-results">
                <div class="results-header">
                    <span id="results-count">找到 0 个视频源</span>
                    <div class="results-actions">
                        <button id="select-all-btn">全选</button>
                        <button id="test-selected-btn">批量测试</button>
                    </div>
                </div>
                <div id="discovery-list" class="discovery-list">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 底部操作 -->
            <div class="discovery-footer">
                <div class="selection-summary">
                    <span id="selected-count">已选择 0 个视频源</span>
                </div>
                <div class="footer-actions">
                    <button id="continue-to-manage-btn" class="primary-btn" disabled>
                        添加到我的播放列表 →
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 2: Personal Management Modal (个人管理) -->
    <div id="personal-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content large-modal">
            <button class="auth-close-btn">&times;</button>
            <div class="step-header">
                <h2>📋 我的视频源</h2>
                <p class="step-description">管理、分类和排序你的个人视频源列表</p>
                <div class="step-indicator">
                    <span class="step completed">✓ 发现筛选</span>
                    <span class="step-arrow">→</span>
                    <span class="step active">2. 个人管理</span>
                </div>
            </div>

            <!-- 简化的提示信息 -->
            <div class="management-hint">
                <p>📝 拖拽视频源进行排序，完成后点击保存</p>
            </div>

            <!-- 个人列表 -->
            <div class="personal-content">
                <div id="personal-channels" class="personal-channels">
                    <!-- 动态生成频道分组 -->
                </div>

                <div class="empty-state" id="empty-personal" style="display: none;">
                    <div class="empty-icon">📺</div>
                    <h3>还没有视频源</h3>
                    <p>去发现页面添加一些视频源吧</p>
                    <button id="back-to-discovery-btn" class="primary-btn">返回发现页面</button>
                </div>
            </div>

            <!-- 底部操作 -->
            <div class="personal-footer">
                <div class="stats-summary">
                    <span>总计: <strong id="total-streams">0</strong> 个视频源</span>
                </div>
                <div class="footer-actions">
                    <button id="back-to-discovery-btn-2" class="secondary-btn">返回上一步</button>
                    <button id="save-and-play-btn" class="primary-btn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 视频预览小窗 -->
    <div id="preview-window" class="preview-window" style="display: none;">
        <div class="preview-header">
            <span id="preview-title">视频预览</span>
            <button id="preview-close" class="preview-close-btn">&times;</button>
        </div>
        <div class="preview-content">
            <video id="preview-video" controls muted></video>
        </div>
    </div>

    <!-- Contribute Modal (提交视频源) -->
    <div id="contribute-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <button class="auth-close-btn">&times;</button>
            <h2>提交视频源</h2>

            <form id="contribute-form">
                <div class="form-group">
                    <label for="stream-name">视频源名称 *</label>
                    <input type="text" id="stream-name" placeholder="例如：CCTV5 高清源" required>
                    <small class="form-hint">建议格式：频道名 + 画质描述</small>
                </div>

                <div class="form-group">
                    <label for="stream-url">视频源地址 *</label>
                    <input type="url" id="stream-url" placeholder="https://example.com/stream.m3u8" required>
                    <small class="form-hint">支持 M3U/M3U8 格式的直播流地址</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="stream-channel">频道分类</label>
                        <select id="stream-channel">
                            <option value="">请选择频道</option>
                            <option value="五星体育">五星体育</option>
                            <option value="CCTV5">CCTV5</option>
                            <option value="广东体育">广东体育</option>
                            <option value="Sky Sports">Sky Sports</option>
                            <option value="ESPN">ESPN</option>
                            <option value="Fox Sports">Fox Sports</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="stream-quality">画质标签</label>
                        <select id="stream-quality">
                            <option value="">请选择画质</option>
                            <option value="4K">4K</option>
                            <option value="1080p">1080p</option>
                            <option value="720p">720p</option>
                            <option value="480p">480p</option>
                            <option value="360p">360p</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="stream-description">描述信息</label>
                    <textarea id="stream-description" placeholder="可选，补充说明这个视频源的特点..." rows="3"></textarea>
                </div>

                <div class="form-actions">
                    <button type="button" id="test-before-submit">测试播放源</button>
                    <button type="submit" id="submit-stream-btn">提交到大仓库</button>
                </div>

                <div id="test-result-display" class="test-result-panel" style="display: none;">
                    <!-- 显示测试结果 -->
                </div>
            </form>
        </div>
    </div>

    <!-- Report Modal (举报视频源) -->
    <div id="report-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal-content">
            <button class="auth-close-btn">&times;</button>
            <h2>举报视频源</h2>

            <div class="report-content">
                <div class="report-stream-info">
                    <h4 id="report-stream-name">视频源名称</h4>
                    <p id="report-stream-url">视频源地址</p>
                </div>

                <form id="report-form">
                    <div class="form-group">
                        <label>举报原因 *</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="report-reason" value="无法播放" required>
                                <span>无法播放</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="report-reason" value="内容不符" required>
                                <span>内容不符</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="report-reason" value="恶意链接" required>
                                <span>恶意链接</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="report-reason" value="其他" required>
                                <span>其他</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="report-details">详细说明</label>
                        <textarea id="report-details" placeholder="请详细描述问题..." rows="4"></textarea>
                    </div>

                    <p class="auth-message" id="report-message"></p>

                    <div class="form-actions">
                        <button type="button" id="cancel-report-btn" class="secondary-btn">取消</button>
                        <button type="submit" class="primary-btn">提交举报</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Auth Modal -->
    <div id="auth-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal-content">
            <button id="auth-close-btn" class="auth-close-btn">&times;</button>

            <!-- Login View -->
            <div id="login-view">
                <h2>欢迎回来</h2>
                <form id="login-form">
                    <input type="email" id="login-email" placeholder="邮箱地址" required>
                    <input type="password" id="login-password" placeholder="密码" required>
                    <p class="auth-message" id="login-message"></p>
                    <button type="submit">登录</button>
                </form>
                <p class="auth-switch">没有账户？ <a href="#" id="show-signup">立即注册</a></p>
                <p class="auth-switch"><a href="#" id="show-reset">忘记密码？</a></p>
            </div>

            <!-- Signup View -->
            <div id="signup-view" style="display: none;">
                <h2>创建账户</h2>
                <form id="signup-form">
                    <input type="email" id="signup-email" placeholder="邮箱地址" required>
                    <input type="password" id="signup-password" placeholder="密码" required>
                    <input type="text" id="signup-invite-code" placeholder="邀请码" required>
                    <div class="turnstile-container">
                        <div class="cf-turnstile" data-sitekey="0x4AAAAAABof2Gg619dti5bh" data-callback="onTurnstileSuccess" data-expired-callback="onTurnstileExpired" data-error-callback="onTurnstileError"></div>
                    </div>
                    <p class="auth-message" id="signup-message"></p>
                    <button type="submit">注册</button>
                </form>
                <p class="auth-switch">已有账户？ <a href="#" id="show-login">立即登录</a></p>
            </div>

            <!-- Reset Password View -->
            <div id="reset-view" style="display: none;">
                <h2>重置密码</h2>
                <form id="reset-form">
                    <input type="email" id="reset-email" placeholder="邮箱地址" required>
                    <p class="auth-message" id="reset-message"></p>
                    <button type="submit">发送重置链接</button>
                </form>
                <p class="auth-switch"><a href="#" id="show-login-from-reset">返回登录</a></p>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div id="change-password-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal-content">
            <button id="change-password-close-btn" class="auth-close-btn">&times;</button>
            <h2>修改密码</h2>
            <form id="change-password-form">
                <input type="password" id="current-password" placeholder="当前密码" required>
                <p class="forgot-password-link">
                    <a href="#" id="forgot-current-password">忘记当前密码？</a>
                </p>
                <input type="password" id="new-password" placeholder="新密码（至少6位）" required>
                <input type="password" id="confirm-password" placeholder="确认新密码" required>
                <p class="auth-message" id="change-password-message"></p>
                <button type="submit">修改密码</button>
            </form>
        </div>
    </div>

    <!-- Reset Password Confirmation Modal -->
    <div id="reset-confirmation-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal-content">
            <button id="reset-confirmation-close-btn" class="auth-close-btn">&times;</button>
            <h2>确认重置密码？</h2>
            <div class="reset-confirmation-content">
                <p>我们将向您的邮箱发送重置链接：</p>
                <p class="user-email-display" id="reset-email-display"></p>
                <p class="reset-warning">重置完成后请重新登录。</p>
                <p class="auth-message" id="reset-confirmation-message"></p>
                <div class="reset-confirmation-buttons">
                    <button type="button" id="cancel-reset-btn" class="secondary-btn">取消</button>
                    <button type="button" id="confirm-reset-btn" class="primary-btn">发送重置邮件</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Configuration Modal -->
    <div id="admin-config-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content large-modal">
            <button id="admin-config-close-btn" class="auth-close-btn">&times;</button>
            <h2>管理员后台</h2>

            <!-- Tab导航 -->
            <div class="admin-tabs">
                <button class="admin-tab active" data-tab="system-config">系统配置</button>
                <button class="admin-tab" data-tab="stream-management">视频源管理</button>
                <button class="admin-tab" data-tab="report-management">举报管理</button>
                <button class="admin-tab" data-tab="operation-logs">操作日志</button>
            </div>

            <!-- 系统配置 -->
            <div id="system-config" class="admin-tab-content active">
                <div class="config-item">
                    <label class="config-label">
                        <input type="checkbox" id="require-invitation-toggle">
                        需要邀请码注册
                    </label>
                    <p class="config-description">开启后，用户注册时必须输入有效的邀请码</p>
                </div>
                <button type="button" id="save-config-btn" class="primary-btn">保存配置</button>
            </div>

            <!-- 视频源管理 -->
            <div id="stream-management" class="admin-tab-content">
                <div class="admin-controls">
                    <div class="filter-controls">
                        <select id="admin-status-filter">
                            <option value="">所有状态</option>
                            <option value="active">正常</option>
                            <option value="pending">待审核</option>
                            <option value="deleted">已删除</option>
                        </select>
                        <input type="text" id="admin-search" placeholder="搜索视频源...">
                        <button id="admin-search-btn">搜索</button>
                    </div>
                    <div class="batch-controls">
                        <button id="batch-approve-btn">批量通过</button>
                        <button id="batch-reject-btn">批量下架</button>
                        <button id="export-streams-btn">导出数据</button>
                    </div>
                </div>
                <div id="admin-streams-list" class="admin-list">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 举报管理 -->
            <div id="report-management" class="admin-tab-content">
                <div class="report-stats">
                    <div class="stat-card">
                        <h4>今日举报</h4>
                        <span id="today-reports">0</span>
                    </div>
                    <div class="stat-card">
                        <h4>待处理</h4>
                        <span id="pending-reports">0</span>
                    </div>
                </div>
                <div id="admin-reports-list" class="admin-list">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 操作日志 -->
            <div id="operation-logs" class="admin-tab-content">
                <div id="admin-logs-list" class="admin-list">
                    <!-- 动态生成 -->
                </div>
            </div>

            <p class="auth-message" id="admin-config-message"></p>
        </div>
    </div>

    <!-- 聊天室浮层 -->
    <div class="chat-float" id="chat-float">
        <span class="chat-float-icon">💬</span>
    </div>

    <script src="script.js"></script>
</body>
</html>