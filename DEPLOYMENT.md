# 视频源系统部署指南

## 📋 部署前准备

### 1. 环境要求
- Supabase 项目（已配置）
- 现代浏览器支持（Chrome, Firefox, Safari, Edge）
- HTTPS 环境（用于HLS视频流播放）

### 2. 依赖检查
确保以下文件已更新：
- `index.html` - 包含所有新的模态框和UI组件
- `style.css` - 包含所有新的样式定义
- `script.js` - 包含所有新的功能代码
- `supabase/migrations/20241205_create_video_source_system.sql` - 数据库迁移文件

## 🗄️ 数据库部署

### 1. 运行数据库迁移

**方法一：使用修复版本的迁移文件（推荐）**
```sql
-- 在 Supabase SQL Editor 中执行
-- 文件：supabase/migrations/20241205_fix_unique_constraint.sql
-- 这个文件修复了唯一约束的语法问题
```

**方法二：如果已经运行了原始迁移文件**
如果你已经运行了 `20241205_create_video_source_system.sql` 并遇到语法错误，请：
1. 先运行修复文件：`supabase/migrations/20241205_fix_unique_constraint.sql`
2. 这将重新创建 stream_reports 表并修复约束问题

**创建的表结构：**
- streams (视频源大仓库)
- user_streams (用户个人列表)
- stream_reports (举报记录) - 使用唯一索引而非约束
- admin_logs (管理员操作日志)

以及相应的索引、RLS策略、函数和触发器

### 2. 验证数据库结构
在 Supabase Dashboard 中确认：
- [ ] 所有表已创建
- [ ] 索引已设置
- [ ] RLS 策略已启用
- [ ] 触发器已创建
- [ ] 函数已创建

### 3. 设置管理员用户
```sql
-- 为管理员用户设置角色
UPDATE auth.users 
SET raw_user_meta_data = raw_user_meta_data || '{"role": "admin"}'::jsonb
WHERE email = '<EMAIL>';
```

## 🚀 前端部署

### 1. 文件上传
将以下文件上传到你的Web服务器：
- `index.html`
- `style.css`
- `script.js`
- 其他静态资源文件

### 2. 配置检查
确认 `script.js` 中的 Supabase 配置正确：
```javascript
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';
```

### 3. HTTPS 配置
确保网站运行在 HTTPS 环境下，这对于：
- HLS 视频流播放
- 现代浏览器的安全要求
- Supabase 连接安全

## 🧪 部署后测试

### 1. 基础功能测试
- [ ] 页面正常加载
- [ ] 用户注册/登录功能正常
- [ ] 所有模态框可以正常打开/关闭

### 2. 数据库连接测试
- [ ] 可以提交视频源
- [ ] 可以查看大仓库列表
- [ ] 可以添加到个人列表

### 3. 权限测试
- [ ] 普通用户功能正常
- [ ] 管理员功能正常
- [ ] 权限控制有效

## 🔧 配置选项

### 1. 系统配置
管理员可以在后台配置：
- 是否需要邀请码注册
- 其他系统参数

### 2. 性能优化
- 启用 Supabase 的 Connection Pooling
- 配置适当的 RLS 策略缓存
- 启用 CDN（如果需要）

## 📊 监控和维护

### 1. 数据库监控
定期检查：
- 数据库性能指标
- 存储空间使用情况
- 查询性能

### 2. 用户反馈
监控：
- 举报数量和类型
- 用户活跃度
- 视频源质量

### 3. 定期清理
建议定期：
- 清理失效的视频源
- 归档旧的操作日志
- 清理无效的举报记录

## 🚨 故障排除

### 常见问题

#### 1. 数据库迁移语法错误
**错误信息：** `ERROR: 42601: syntax error at or near "(" UNIQUE(stream_id, reporter_id, DATE(created_at))`

**解决方案：**
- 使用修复版本的迁移文件：`supabase/migrations/20241205_fix_unique_constraint.sql`
- 这个文件使用唯一索引替代了有问题的唯一约束语法

#### 2. 视频源测试失败
- 检查 HLS.js 库是否正确加载
- 确认网站运行在 HTTPS 环境
- 检查浏览器控制台错误信息

#### 2. 权限错误
- 检查 RLS 策略是否正确设置
- 确认用户角色配置正确
- 检查 Supabase 项目权限设置

#### 3. 数据库连接问题
- 检查 Supabase URL 和 API Key
- 确认网络连接正常
- 检查 Supabase 项目状态

#### 4. UI 显示问题
- 检查 CSS 文件是否正确加载
- 确认浏览器兼容性
- 检查控制台 JavaScript 错误

## 📈 扩展建议

### 1. 性能优化
- 实现视频源缓存机制
- 添加 CDN 支持
- 优化数据库查询

### 2. 功能扩展
- 添加视频源评分系统
- 实现用户评论功能
- 添加视频源分类标签

### 3. 监控增强
- 添加用户行为分析
- 实现实时监控面板
- 添加自动化测试

## 📞 技术支持

如遇到部署问题，请检查：
1. 浏览器控制台错误信息
2. Supabase 项目日志
3. 网络连接状态
4. 数据库表结构

部署完成后，建议使用提供的测试清单 `test-checklist.md` 进行全面测试。
