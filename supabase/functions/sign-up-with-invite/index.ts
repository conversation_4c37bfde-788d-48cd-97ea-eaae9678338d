import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

serve(async (req) => {
  // This is needed if you're planning to invoke your function from a browser.
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { email, password, invitation_code, turnstile_token } = await req.json()

    // 1. 验证 Turnstile token
    if (!turnstile_token) {
      return new Response(JSON.stringify({ error: '请完成人机验证。' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // 验证 Turnstile token
    const turnstileResponse = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        secret: Deno.env.get('TURNSTILE_SECRET_KEY') ?? '',
        response: turnstile_token,
      }),
    })

    const turnstileResult = await turnstileResponse.json()
    if (!turnstileResult.success) {
      return new Response(JSON.stringify({ error: '人机验证失败，请重试。' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // 2. 检查是否需要邀请码
    const { data: configData, error: configError } = await supabaseAdmin
      .from('system_config')
      .select('config_value')
      .eq('config_key', 'require_invitation_code')
      .single()

    const requireInvitationCode = configData?.config_value === 'true'
    let codeData = null

    // 3. 如果需要邀请码，则验证邀请码
    if (requireInvitationCode) {
      if (!invitation_code) {
        return new Response(JSON.stringify({ error: '请输入邀请码。' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      const { data: inviteCodeData, error: codeError } = await supabaseAdmin
        .from('invitation_codes')
        .select('id, is_used')
        .eq('code', invitation_code)
        .single()

      if (codeError || !inviteCodeData) {
        return new Response(JSON.stringify({ error: '无效的邀请码。' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      if (inviteCodeData.is_used) {
        return new Response(JSON.stringify({ error: '此邀请码已被使用。' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      codeData = inviteCodeData
    }

    // 4. Create the user
    const { data: { user }, error: signupError } = await supabaseAdmin.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true, // Auto-confirm user for simplicity
    })

    if (signupError) {
      return new Response(JSON.stringify({ error: signupError.message }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // 5. 如果使用了邀请码，标记为已使用
    if (requireInvitationCode && codeData) {
      try {
        const { error: updateError } = await supabaseAdmin
          .from('invitation_codes')
          .update({ is_used: true, used_by_user_id: user.id, used_at: new Date().toISOString() })
          .eq('id', codeData.id)

        if (updateError) {
          console.error('Failed to mark invitation code as used:', updateError)
          // 注意：这里不返回错误，因为用户已经创建成功
          // 只是记录日志，避免影响用户注册流程
        }
      } catch (updateException) {
        console.error('Exception when updating invitation code:', updateException)
        // 同样不返回错误，只记录日志
      }
    }

    return new Response(JSON.stringify({ user }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})