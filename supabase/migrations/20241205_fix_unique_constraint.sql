-- 修复举报表的唯一约束问题并更新streams表结构
-- 创建时间: 2024-12-05

-- 1. 首先更新现有的streams表，添加缺失的字段
ALTER TABLE public.streams
ADD COLUMN IF NOT EXISTS channel TEXT,
ADD COLUMN IF NOT EXISTS quality TEXT,
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS usage_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_used_at TIMESTAMP;

-- 更新status字段的约束，添加新的状态
ALTER TABLE public.streams
DROP CONSTRAINT IF EXISTS streams_status_check;

ALTER TABLE public.streams
ADD CONSTRAINT streams_status_check
CHECK (status IN ('active', 'pending', 'deleted', 'rejected'));

-- 2. 创建user_streams表（如果不存在）
CREATE TABLE IF NOT EXISTS public.user_streams (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stream_id BIGINT REFERENCES streams(id) ON DELETE CASCADE,
    custom_name TEXT,                          -- 用户自定义名称
    sort_order INT DEFAULT 0,                  -- 排序权重
    is_favorite BOOLEAN DEFAULT FALSE,         -- 是否收藏
    added_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, stream_id)
);

-- 3. 创建admin_logs表（如果不存在）
CREATE TABLE IF NOT EXISTS public.admin_logs (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    admin_id UUID REFERENCES auth.users(id),
    action TEXT NOT NULL,                      -- 操作类型：delete, restore, approve, reject
    target_type TEXT NOT NULL,                 -- 目标类型：stream
    target_id BIGINT,                          -- 目标ID
    reason TEXT,                               -- 操作原因
    created_at TIMESTAMP DEFAULT NOW()
);

-- 4. 如果之前的迁移失败，先删除可能存在的stream_reports表
DROP TABLE IF EXISTS public.stream_reports CASCADE;

-- 重新创建举报记录表（不使用函数表达式的唯一约束）
CREATE TABLE public.stream_reports (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    stream_id BIGINT REFERENCES streams(id) ON DELETE CASCADE,
    reporter_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,                      -- 举报原因
    created_at TIMESTAMP DEFAULT NOW()
);

-- 创建唯一索引来防止同一用户同一天重复举报同一视频源
CREATE UNIQUE INDEX idx_stream_reports_unique_daily 
ON stream_reports(stream_id, reporter_id, DATE(created_at));

-- 创建其他必要的索引
CREATE INDEX idx_stream_reports_stream ON stream_reports(stream_id);
CREATE INDEX idx_stream_reports_date ON stream_reports(DATE(created_at));

-- 创建所有必要的索引
CREATE INDEX IF NOT EXISTS idx_streams_status ON streams(status);
CREATE INDEX IF NOT EXISTS idx_streams_submitter ON streams(submitter_id);
CREATE INDEX IF NOT EXISTS idx_streams_created_at ON streams(created_at);
CREATE INDEX IF NOT EXISTS idx_user_streams_user ON user_streams(user_id);
CREATE INDEX IF NOT EXISTS idx_user_streams_sort ON user_streams(user_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_admin_logs_admin ON admin_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_logs_target ON admin_logs(target_type, target_id);

-- 启用所有表的RLS
ALTER TABLE public.streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stream_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_logs ENABLE ROW LEVEL SECURITY;

-- 创建所有表的RLS策略

-- streams表策略
DROP POLICY IF EXISTS "Allow public read active streams" ON public.streams;
CREATE POLICY "Allow public read active streams" ON public.streams
    FOR SELECT USING (status = 'active');

DROP POLICY IF EXISTS "Allow authenticated users to insert streams" ON public.streams;
CREATE POLICY "Allow authenticated users to insert streams" ON public.streams
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow admin to manage all streams" ON public.streams;
CREATE POLICY "Allow admin to manage all streams" ON public.streams
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin' OR
        (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
    );

-- user_streams表策略
DROP POLICY IF EXISTS "Users can manage their own streams" ON public.user_streams;
CREATE POLICY "Users can manage their own streams" ON public.user_streams
    FOR ALL USING (auth.uid() = user_id);

-- stream_reports表策略
DROP POLICY IF EXISTS "Users can insert reports" ON public.stream_reports;
CREATE POLICY "Users can insert reports" ON public.stream_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

DROP POLICY IF EXISTS "Admin can view all reports" ON public.stream_reports;
CREATE POLICY "Admin can view all reports" ON public.stream_reports
    FOR SELECT USING (
        auth.jwt() ->> 'role' = 'admin' OR
        (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin' OR
        (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
    );

-- admin_logs表策略
DROP POLICY IF EXISTS "Admin can manage logs" ON public.admin_logs;
CREATE POLICY "Admin can manage logs" ON public.admin_logs
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin' OR
        (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
    );

-- 重新创建检查举报的函数
CREATE OR REPLACE FUNCTION check_and_handle_reports()
RETURNS TRIGGER AS $$
DECLARE
    report_count INTEGER;
BEGIN
    -- 检查今天对该视频源的举报数量
    SELECT COUNT(DISTINCT reporter_id) INTO report_count
    FROM stream_reports 
    WHERE stream_id = NEW.stream_id 
    AND DATE(created_at) = CURRENT_DATE;
    
    -- 如果举报数量达到3个，将状态改为pending
    IF report_count >= 3 THEN
        UPDATE streams 
        SET status = 'pending' 
        WHERE id = NEW.stream_id AND status = 'active';
        
        -- 记录日志
        BEGIN
            INSERT INTO admin_logs (admin_id, action, target_type, target_id, reason, created_at)
            VALUES (NULL, 'auto_suspend', 'stream', NEW.stream_id,
                    '自动下架：收到' || report_count || '个举报', NOW());
        EXCEPTION
            WHEN OTHERS THEN
                -- 忽略日志记录错误，不影响主要功能
                NULL;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_check_reports ON stream_reports;
CREATE TRIGGER trigger_check_reports
    AFTER INSERT ON stream_reports
    FOR EACH ROW
    EXECUTE FUNCTION check_and_handle_reports();

-- 创建检查用户今天是否已经举报过某个视频源的函数
CREATE OR REPLACE FUNCTION check_user_daily_report(
    p_stream_id BIGINT,
    p_reporter_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    report_exists BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM stream_reports 
        WHERE stream_id = p_stream_id 
        AND reporter_id = p_reporter_id 
        AND DATE(created_at) = CURRENT_DATE
    ) INTO report_exists;
    
    RETURN report_exists;
END;
$$ LANGUAGE plpgsql;

-- 创建更新使用统计的函数
CREATE OR REPLACE FUNCTION update_stream_usage(stream_id_param BIGINT)
RETURNS void AS $$
BEGIN
    UPDATE streams
    SET
        usage_count = COALESCE(usage_count, 0) + 1,
        last_used_at = NOW()
    WHERE id = stream_id_param;
END;
$$ LANGUAGE plpgsql;

-- 创建获取视频源统计的函数
CREATE OR REPLACE FUNCTION get_stream_stats()
RETURNS TABLE(
    total_streams BIGINT,
    active_streams BIGINT,
    pending_streams BIGINT,
    deleted_streams BIGINT,
    today_reports BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM streams WHERE status != 'deleted') as total_streams,
        (SELECT COUNT(*) FROM streams WHERE status = 'active') as active_streams,
        (SELECT COUNT(*) FROM streams WHERE status = 'pending') as pending_streams,
        (SELECT COUNT(*) FROM streams WHERE status = 'deleted') as deleted_streams,
        (SELECT COUNT(*) FROM stream_reports WHERE DATE(created_at) = CURRENT_DATE) as today_reports;
END;
$$ LANGUAGE plpgsql;

-- 验证修复
-- 可以运行以下查询来验证表结构是否正确：
-- SELECT * FROM information_schema.tables WHERE table_name IN ('streams', 'user_streams', 'stream_reports', 'admin_logs');
-- SELECT * FROM pg_indexes WHERE tablename = 'stream_reports';
