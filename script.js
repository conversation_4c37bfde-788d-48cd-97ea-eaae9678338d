document.addEventListener('DOMContentLoaded', () => {
    // --- Supabase Setup ---
    const SUPABASE_URL = 'https://znidotclrjksnizomrmh.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpuaWRvdGNscmprc25pem9tcm1oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQzMjM0OTYsImV4cCI6MjA2OTg5OTQ5Nn0.16oxptca7i9l_kO6mvAlc8ProhQ5UfhQ9RToShzvm5M';
    const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    // --- Element Getters ---
    const getVideoPlayer = () => document.getElementById('video-player');
    const getM3uInput = () => document.getElementById('m3u-input');
    const getLoadStreamBtn = () => document.getElementById('load-stream-btn');
    const getStreamDropdown = () => document.getElementById('stream-dropdown');
    const getDanmakuInput = () => document.getElementById('danmaku-input');
    const getSendDanmakuBtn = () => document.getElementById('send-danmaku-btn');
    const getDanmakuOverlay = () => document.querySelector('.danmaku-overlay');
    const getLoginBtn = () => document.getElementById('login-btn');
    const getLogoutBtn = () => document.getElementById('logout-btn');
    const getUserInfo = () => document.getElementById('user-info');
    const getContributeBtn = () => document.getElementById('contribute-btn');
    const getScheduleBtn = () => document.getElementById('schedule-btn');
    const getStandingsBtn = () => document.getElementById('standings-btn');
    const getFilterSourcesBtn = () => document.getElementById('filter-sources-btn');
    const getScheduleModal = () => document.getElementById('schedule-modal');
    const getStandingsModal = () => document.getElementById('standings-modal');
    const getWarehouseModal = () => document.getElementById('warehouse-modal');
    const getContributeModal = () => document.getElementById('contribute-modal');
    const getReportModal = () => document.getElementById('report-modal');
    const getAuthModal = () => document.getElementById('auth-modal');
    const getChatCloseBtn = () => document.getElementById('chat-close-btn');
    const getChatSection = () => document.getElementById('chat-section');
    const getChatFloat = () => document.getElementById('chat-float');
    const getChatInput = () => document.getElementById('chat-input');
    const getSendChatBtn = () => document.getElementById('send-chat-btn');
    const getChatMessages = () => document.getElementById('chat-messages');
    const getManualInputSection = () => document.getElementById('manual-input-section');
    const getChannelSourcesSection = () => document.getElementById('channel-sources-section');

    let hls = null;

    // --- Toast Notification Function ---
    function showToast(message, type = 'info') {
        // 移除已存在的 toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建 toast 元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 添加到页面
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => toast.classList.add('show'), 100);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // --- HLS Video Loading Logic ---
    function loadStream(sourceUrl) {
        const video = getVideoPlayer();
        if (!video) return;
        if (!sourceUrl) {
            showToast('请输入有效的 M3U/M3U8 链接', 'warning');
            return;
        }
        if (Hls.isSupported()) {
            if (hls) hls.destroy();
            hls = new Hls();
            hls.loadSource(sourceUrl);
            hls.attachMedia(video);
            hls.on(Hls.Events.MANIFEST_PARSED, () => {
                video.play().catch(e => console.error("播放失败：", e));
                localStorage.setItem('lastStreamUrl', sourceUrl);
            });
            hls.on(Hls.Events.ERROR, (_, data) => {
                if (data.fatal) {
                    console.error('HLS 加载发生致命错误:', data);
                    showToast('视频源加载失败，请检查链接或更换线路', 'error');
                }
            });
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = sourceUrl;
            video.addEventListener('loadedmetadata', () => {
                video.play().catch(e => console.error("播放失败：", e));
                localStorage.setItem('lastStreamUrl', sourceUrl);
            });
        } else {
            showToast('您的浏览器不支持 HLS 播放', 'error');
        }
    }

    // --- Danmaku Logic ---
    async function sendDanmaku() {
        const danmakuInput = getDanmakuInput();
        if (!danmakuInput) return;
        const content = danmakuInput.value.trim();
        if (!content) return;
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            const authModal = getAuthModal();
            if (authModal) authModal.style.display = 'flex';
            return;
        }
        const { error } = await supabase.from('danmaku').insert({ content: content, user_id: user.id });
        if (error) {
            console.error('发送弹幕失败:', error);
            showToast('弹幕发送失败，请稍后重试', 'error');
        } else {
            danmakuInput.value = '';
            showToast('弹幕发送成功', 'success');
        }
    }

    function displayDanmaku(danmaku) {
        const danmakuOverlay = getDanmakuOverlay();
        if (!danmakuOverlay) return;
        const danmakuElement = document.createElement('div');
        danmakuElement.classList.add('danmaku');
        danmakuElement.textContent = danmaku.content;
        const randomTop = Math.floor(Math.random() * 80) + 5;
        danmakuElement.style.top = `${randomTop}%`;
        danmakuOverlay.appendChild(danmakuElement);
        danmakuElement.addEventListener('animationend', () => danmakuElement.remove());
    }

    function subscribeToDanmaku() {
        try {
            supabase.channel('public:danmaku')
                .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'danmaku' }, payload => {
                    displayDanmaku(payload.new);
                })
                .subscribe();
        } catch (e) {
            console.error("订阅弹幕失败: ", e);
        }
    }

    // --- 聊天室功能 ---
    function closeChat() {
        const chatSection = getChatSection();
        const chatFloat = getChatFloat();
        if (!chatSection || !chatFloat) return;

        chatSection.classList.add('hidden');
        chatFloat.classList.add('show');
    }

    function openChat() {
        const chatSection = getChatSection();
        const chatFloat = getChatFloat();
        if (!chatSection || !chatFloat) return;

        chatSection.classList.remove('hidden');
        chatFloat.classList.remove('show');
    }

    async function sendChatMessage() {
        const chatInput = getChatInput();
        if (!chatInput) return;

        const content = chatInput.value.trim();
        if (!content) return;

        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            showToast('请先登录后再发送消息', 'warning');
            const authModal = getAuthModal();
            if (authModal) authModal.style.display = 'flex';
            return;
        }

        // 这里可以添加发送聊天消息到数据库的逻辑
        // 暂时只在本地显示
        displayChatMessage({
            content: content,
            user_email: user.email,
            created_at: new Date().toISOString()
        });

        chatInput.value = '';
    }

    function displayChatMessage(message) {
        const chatMessages = getChatMessages();
        if (!chatMessages) return;

        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';

        const time = new Date(message.created_at).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageElement.innerHTML = `
            <div class="chat-message-header">
                <span class="chat-user">${message.user_email.split('@')[0]}</span>
                <span class="chat-time">${time}</span>
            </div>
            <div class="chat-message-content">${message.content}</div>
        `;

        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // --- 视频源测试功能 ---
    async function testStreamConnection(url) {
        return new Promise((resolve, reject) => {
            const video = document.createElement('video');
            video.style.display = 'none';
            document.body.appendChild(video);

            const startTime = Date.now();
            let timeout;

            if (Hls.isSupported()) {
                const hls = new Hls();

                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    const loadTime = Date.now() - startTime;
                    cleanup();
                    resolve({ loadTime });
                });

                hls.on(Hls.Events.ERROR, (event, data) => {
                    if (data.fatal) {
                        cleanup();
                        reject(new Error(data.details || '连接失败'));
                    }
                });

                // 5秒超时
                timeout = setTimeout(() => {
                    cleanup();
                    reject(new Error('连接超时'));
                }, 5000);

                hls.loadSource(url);
                hls.attachMedia(video);

                function cleanup() {
                    clearTimeout(timeout);
                    hls.destroy();
                    document.body.removeChild(video);
                }
            } else {
                // 不支持HLS
                document.body.removeChild(video);
                reject(new Error('浏览器不支持HLS'));
            }
        });
    }

    // 单个视频源测试（最多3次重试）
    async function testSingleStream(streamId, retryCount = 0) {
        const maxRetries = 3;
        const testResult = document.getElementById(`test-result-${streamId}`);
        const testProgress = document.getElementById(`test-progress-${streamId}`);

        if (!testResult) return;

        // 显示测试进度
        testResult.textContent = '测试中...';
        testResult.className = 'test-result testing';
        if (testProgress) testProgress.style.display = 'block';

        try {
            const stream = await getStreamById(streamId);
            if (!stream) {
                throw new Error('视频源不存在');
            }

            const result = await testStreamConnection(stream.url);

            testResult.textContent = `✅ 可用 (${result.loadTime}ms)`;
            testResult.className = 'test-result success';

        } catch (error) {
            if (retryCount < maxRetries - 1) {
                // 重试
                testResult.textContent = `重试中... (${retryCount + 1}/${maxRetries})`;
                setTimeout(() => testSingleStream(streamId, retryCount + 1), 1000);
            } else {
                // 3次都失败，标记为失效
                testResult.textContent = '❌ 失效';
                testResult.className = 'test-result failed';
            }
        } finally {
            if (testProgress) testProgress.style.display = 'none';
        }
    }

    // 获取视频源信息
    async function getStreamById(streamId) {
        try {
            const { data, error } = await supabase
                .from('streams')
                .select('*')
                .eq('id', streamId)
                .single();

            if (error) {
                console.error('获取视频源失败:', error);
                return null;
            }

            return data;
        } catch (error) {
            console.error('获取视频源异常:', error);
            return null;
        }
    }

    // --- 举报功能 ---
    let currentReportStreamId = null;

    function showReportModal(streamId, streamName, streamUrl) {
        currentReportStreamId = streamId;

        document.getElementById('report-stream-name').textContent = streamName;
        document.getElementById('report-stream-url').textContent = streamUrl;

        const reportModal = getReportModal();
        if (reportModal) {
            reportModal.style.display = 'flex';
        }
    }

    function closeReportModal() {
        const reportModal = getReportModal();
        if (reportModal) {
            reportModal.style.display = 'none';
        }

        // 重置表单
        const reportForm = document.getElementById('report-form');
        if (reportForm) {
            reportForm.reset();
        }

        const reportMessage = document.getElementById('report-message');
        if (reportMessage) {
            reportMessage.textContent = '';
            reportMessage.className = 'auth-message';
        }

        currentReportStreamId = null;
    }

    async function submitReport() {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            showToast('请先登录', 'warning');
            return;
        }

        const reasonRadios = document.querySelectorAll('input[name="report-reason"]');
        const selectedReason = Array.from(reasonRadios).find(radio => radio.checked);
        const details = document.getElementById('report-details').value.trim();
        const reportMessage = document.getElementById('report-message');

        if (!selectedReason) {
            reportMessage.textContent = '请选择举报原因';
            reportMessage.className = 'auth-message error';
            return;
        }

        const reason = details ? `${selectedReason.value}: ${details}` : selectedReason.value;

        try {
            reportMessage.textContent = '正在提交举报...';
            reportMessage.className = 'auth-message';

            // 先检查用户今天是否已经举报过这个视频源
            const { data: hasReported, error: checkError } = await supabase
                .rpc('check_user_daily_report', {
                    p_stream_id: currentReportStreamId,
                    p_reporter_id: user.id
                });

            if (checkError) {
                console.error('检查举报状态失败:', checkError);
                reportMessage.textContent = '检查举报状态失败，请稍后重试';
                reportMessage.className = 'auth-message error';
                return;
            }

            if (hasReported) {
                reportMessage.textContent = '您今天已经举报过这个视频源了';
                reportMessage.className = 'auth-message error';
                return;
            }

            // 提交举报
            const { error } = await supabase
                .from('stream_reports')
                .insert({
                    stream_id: currentReportStreamId,
                    reporter_id: user.id,
                    reason: reason
                });

            if (error) {
                console.error('举报提交失败:', error);
                if (error.code === '23505') { // 唯一索引违反
                    reportMessage.textContent = '您今天已经举报过这个视频源了';
                    reportMessage.className = 'auth-message error';
                } else {
                    reportMessage.textContent = '举报失败，请稍后重试';
                    reportMessage.className = 'auth-message error';
                }
            } else {
                showToast('举报提交成功，感谢您的反馈', 'success');
                closeReportModal();
            }
        } catch (error) {
            console.error('举报异常:', error);
            reportMessage.textContent = '举报失败，请检查网络连接';
            reportMessage.className = 'auth-message error';
        }
    }

    // --- Stream Contribution & Selection Logic ---
    // --- 用户状态管理 ---
    function updateUIForUserState(user) {
        const manualInputSection = getManualInputSection();
        const channelSourcesSection = getChannelSourcesSection();

        if (user) {
            // 登录状态：显示频道列表，隐藏未登录的手动输入
            if (manualInputSection) manualInputSection.style.display = 'none';
            if (channelSourcesSection) channelSourcesSection.style.display = 'block';
            loadChannelsAndSources();
        } else {
            // 未登录状态：显示手动输入，隐藏频道列表
            if (manualInputSection) manualInputSection.style.display = 'block';
            if (channelSourcesSection) channelSourcesSection.style.display = 'none';
        }
    }

    async function loadChannelsAndSources() {
        // 现在使用真实的用户个人列表数据
        await loadUserPersonalStreams();
    }

    function displayChannels(channels) {
        const channelTabs = document.getElementById('channel-tabs');
        if (!channelTabs) return;

        channelTabs.innerHTML = '';
        channels.forEach(channel => {
            const tab = document.createElement('button');
            tab.className = `channel-tab ${channel.active ? 'active' : ''}`;
            tab.textContent = channel.name;
            tab.onclick = () => switchChannel(channel.id, channels);
            channelTabs.appendChild(tab);
        });
    }

    function switchChannel(channelId, channels) {
        // 更新频道标签状态
        const tabs = document.querySelectorAll('.channel-tab');
        tabs.forEach((tab, index) => {
            tab.classList.toggle('active', channels[index].id === channelId);
        });

        // 重新加载对应频道的视频源
        loadChannelsAndSources();
    }

    function displaySources(sources, activeChannelId) {
        const sourcesList = document.getElementById('sources-list');
        if (!sourcesList) return;

        const channelSources = sources.filter(s => s.channel_id === activeChannelId);
        sourcesList.innerHTML = '';

        channelSources.forEach(source => {
            const sourceItem = document.createElement('div');
            sourceItem.className = 'source-item';
            sourceItem.onclick = () => selectSource(source);

            sourceItem.innerHTML = `
                <div class="source-name">${source.name}</div>
                <div class="source-quality">${source.quality}</div>
            `;

            sourcesList.appendChild(sourceItem);
        });
    }

    function selectSource(source) {
        // 更新选中状态
        document.querySelectorAll('.source-item').forEach(item => {
            item.classList.remove('active');
        });
        event.currentTarget.classList.add('active');

        // 加载视频源
        loadStream(source.url);
        showToast(`已切换到：${source.name}`, 'success');
    }

    // --- Tab切换功能 ---
    function switchSourceTab(tabName) {
        // 更新tab按钮状态
        document.querySelectorAll('.source-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.getElementById(tabName + '-tab').classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName + '-content').classList.add('active');
    }

    async function fetchAndDisplayStreams() {
        // 保留原有的下拉菜单功能（用于未登录用户的社区线路）
        const streamDropdown = getStreamDropdown();
        if (!streamDropdown) return;
        const { data, error } = await supabase.from('streams').select('name, url').eq('status', 'approved');
        if (error) {
            console.error('获取社区线路失败:', error);
            return;
        }
        if (!data) return;
        while (streamDropdown.options.length > 1) streamDropdown.remove(1);
        data.forEach(stream => {
            const option = document.createElement('option');
            option.value = stream.url;
            option.textContent = stream.name;
            streamDropdown.appendChild(option);
        });
    }

    // --- 视频源提交功能 ---
    async function handleContributeStream() {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            showToast('请先登录后再提交视频源', 'warning');
            const authModal = getAuthModal();
            if (authModal) authModal.style.display = 'flex';
            return;
        }

        // 显示提交模态框
        const contributeModal = getContributeModal();
        if (contributeModal) {
            contributeModal.style.display = 'flex';
        }
    }

    function closeContributeModal() {
        const contributeModal = getContributeModal();
        if (contributeModal) {
            contributeModal.style.display = 'none';
        }

        // 重置表单
        const contributeForm = document.getElementById('contribute-form');
        if (contributeForm) {
            contributeForm.reset();
        }

        // 隐藏测试结果
        const testResultDisplay = document.getElementById('test-result-display');
        if (testResultDisplay) {
            testResultDisplay.style.display = 'none';
        }
    }

    async function testStreamBeforeSubmit() {
        const streamUrl = document.getElementById('stream-url').value.trim();
        const testResultDisplay = document.getElementById('test-result-display');

        if (!streamUrl) {
            showToast('请先输入视频源地址', 'warning');
            return;
        }

        if (!streamUrl.startsWith('http')) {
            showToast('请输入有效的HTTP/HTTPS地址', 'warning');
            return;
        }

        testResultDisplay.style.display = 'block';
        testResultDisplay.innerHTML = '<div class="test-progress">正在测试连接...</div>';

        try {
            const result = await testStreamConnection(streamUrl);
            testResultDisplay.innerHTML = `
                <div class="test-success">
                    ✅ 测试成功！连接时间: ${result.loadTime}ms
                </div>
            `;
        } catch (error) {
            testResultDisplay.innerHTML = `
                <div class="test-error">
                    ❌ 测试失败: ${error.message}
                </div>
            `;
        }
    }

    async function submitStreamToWarehouse() {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            showToast('请先登录', 'warning');
            return;
        }

        const name = document.getElementById('stream-name').value.trim();
        const url = document.getElementById('stream-url').value.trim();
        const channel = document.getElementById('stream-channel').value;
        const quality = document.getElementById('stream-quality').value;
        const description = document.getElementById('stream-description').value.trim();

        // 前端验证
        if (!name || !url) {
            showToast('请填写必填字段', 'warning');
            return;
        }

        if (!url.startsWith('http')) {
            showToast('请输入有效的HTTP/HTTPS地址', 'warning');
            return;
        }

        try {
            // 检查URL是否已存在
            const { data: existing, error: checkError } = await supabase
                .from('streams')
                .select('id')
                .eq('url', url)
                .eq('status', 'active')
                .maybeSingle();

            if (checkError) {
                console.error('检查重复URL失败:', checkError);
                // 继续执行，不阻止提交
            }

            if (existing) {
                showToast('该视频源已存在', 'warning');
                return;
            }

            // 提交到数据库
            const { error } = await supabase
                .from('streams')
                .insert({
                    name,
                    url,
                    channel,
                    quality,
                    description,
                    submitter_id: user.id,
                    status: 'active'  // 直接设为active，无需审核
                });

            if (error) {
                console.error('提交失败:', error);
                showToast('提交失败，请稍后重试', 'error');
            } else {
                showToast('提交成功！视频源已添加到大仓库', 'success');
                closeContributeModal();
                // 如果大仓库正在显示，刷新列表
                refreshWarehouseList();
            }
        } catch (error) {
            console.error('提交异常:', error);
            showToast('提交失败，请检查网络连接', 'error');
        }
    }

    // --- 新的两步式流程功能 ---
    let currentPage = 1;
    const pageSize = 20;
    let currentFilters = {
        search: '',
        channel: '',
        quality: '',
        quickFilter: ''
    };

    // 统一使用全局状态管理，移除局部变量
    // let selectedStreams = new Set(); // 已删除，统一使用 window.selectedStreams

    // 第一步：发现页面
    function showDiscoveryModal() {
        const discoveryModal = document.getElementById('discovery-modal');
        if (discoveryModal) {
            discoveryModal.style.display = 'flex';
            initializeDiscoveryPage();
        }
    }

    function closeDiscoveryModal() {
        const discoveryModal = document.getElementById('discovery-modal');
        if (discoveryModal) {
            discoveryModal.style.display = 'none';
            // 关闭时不清空选择状态，保持用户的选择
            // 如果需要清空，应该由用户主动操作
        }
    }

    function initializeDiscoveryPage() {
        // 重置筛选条件
        currentFilters = {
            search: '',
            channel: '',
            quality: '',
            quickFilter: ''
        };

        // 初始化全局选择集合（如果不存在）
        window.selectedStreams = window.selectedStreams || new Set();

        // 加载初始数据
        loadDiscoveryStreams();

        // 更新UI状态
        updateSelectionCount();
        updateContinueButton();
    }

    function bindQuickFilterEvents() {
        const quickFilterBtns = document.querySelectorAll('.filter-tag');
        quickFilterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除其他按钮的active状态
                quickFilterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // 设置快速筛选条件
                currentFilters.quickFilter = btn.dataset.filter;
                loadDiscoveryStreams();
            });
        });
    }

    // 删除重复的loadDiscoveryStreams函数，使用后面更完整的版本

    // 第二步：个人管理页面
    function showPersonalModal() {
        const personalModal = document.getElementById('personal-modal');
        if (personalModal) {
            personalModal.style.display = 'flex';
            loadPersonalManagementPage();
        }
    }

    function closePersonalModal() {
        const personalModal = document.getElementById('personal-modal');
        if (personalModal) {
            personalModal.style.display = 'none';
        }
    }

    // 删除重复的函数定义，使用后面的全局版本
    // continueToManage 和 addSelectedStreamsToPersonal 已在全局定义

    // 选择控制函数
    // DOMContentLoaded内部的函数已废弃，HTML直接调用全局函数
    // 移除重复的toggleStreamSelection函数定义
    // 删除重复的updateSelectionCount函数，使用后面的全局版本

    function updateContinueButton() {
        const continueBtn = document.getElementById('continue-to-manage-btn');
        // 使用全局变量
        const count = window.selectedStreams ? window.selectedStreams.size : 0;
        if (continueBtn) {
            continueBtn.disabled = count === 0;
        }
    }

    // 移除DOMContentLoaded内部的selectAllStreams函数，使用全局函数

    // 移除DOMContentLoaded内部的testSelectedStreams函数，使用全局函数

    async function loadWarehouseStreams(page = 1) {
        currentPage = page;
        const streamWarehouse = document.getElementById('stream-warehouse');
        if (!streamWarehouse) return;

        try {
            streamWarehouse.innerHTML = '<div class="loading">正在加载...</div>';

            let query = supabase
                .from('streams')
                .select(`
                    id, name, url, channel, quality, description, created_at, usage_count, submitter_id
                `)
                .eq('status', 'active')
                .range((page - 1) * pageSize, page * pageSize - 1);

            // 应用筛选条件
            if (currentFilters.search) {
                query = query.ilike('name', `%${currentFilters.search}%`);
            }
            if (currentFilters.channel) {
                query = query.eq('channel', currentFilters.channel);
            }
            if (currentFilters.quality) {
                query = query.eq('quality', currentFilters.quality);
            }

            // 应用排序
            switch (currentFilters.sort) {
                case 'name':
                    query = query.order('name', { ascending: true });
                    break;
                case 'usage':
                    query = query.order('usage_count', { ascending: false });
                    break;
                default: // newest
                    query = query.order('created_at', { ascending: false });
            }

            const { data, error, count } = await query;

            if (error) {
                console.error('加载大仓库失败:', error);
                streamWarehouse.innerHTML = '<div class="error">加载失败，请稍后重试</div>';
                return;
            }

            displayWarehouseStreams(data || []);
            updatePagination(count || 0);

        } catch (error) {
            console.error('加载大仓库异常:', error);
            streamWarehouse.innerHTML = '<div class="error">加载异常，请检查网络连接</div>';
        }
    }

    function displayWarehouseStreams(streams) {
        const streamWarehouse = document.getElementById('stream-warehouse');
        if (!streamWarehouse) return;

        if (streams.length === 0) {
            streamWarehouse.innerHTML = '<div class="empty">暂无视频源</div>';
            return;
        }

        streamWarehouse.innerHTML = streams.map(stream => `
            <div class="stream-item" data-stream-id="${stream.id}">
                <div class="stream-checkbox">
                    <input type="checkbox" class="stream-select" value="${stream.id}">
                </div>
                <div class="stream-info">
                    <h4 class="stream-name">${stream.name}</h4>
                    <p class="stream-url">${stream.url}</p>
                    <div class="stream-tags">
                        ${stream.channel ? `<span class="tag channel">${stream.channel}</span>` : ''}
                        ${stream.quality ? `<span class="tag quality">${stream.quality}</span>` : ''}
                    </div>
                    <small class="stream-meta">
                        ${formatDate(stream.created_at)} ·
                        使用 ${stream.usage_count || 0} 次
                    </small>
                </div>
                <div class="stream-status">
                    <span class="test-result" id="test-result-${stream.id}">未测试</span>
                    <div class="test-progress" id="test-progress-${stream.id}" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                </div>
                <div class="stream-actions">
                    <button onclick="testSingleStream(${stream.id})">测试</button>
                    <button onclick="previewStream(${stream.id})">预览</button>
                    <button onclick="addToPersonalList(${stream.id})">添加到我的列表</button>
                    <button onclick="showReportModal(${stream.id}, '${stream.name}', '${stream.url}')">举报</button>
                </div>
            </div>
        `).join('');
    }

    function updatePagination(totalCount) {
        const totalPages = Math.ceil(totalCount / pageSize);
        const pageInfo = document.getElementById('page-info');
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');

        if (pageInfo) {
            pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
        }

        if (prevBtn) {
            prevBtn.disabled = currentPage <= 1;
        }

        if (nextBtn) {
            nextBtn.disabled = currentPage >= totalPages;
        }
    }

    function refreshWarehouseList() {
        if (document.getElementById('warehouse-modal').style.display === 'flex') {
            loadWarehouseStreams(currentPage);
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // --- 个人列表管理功能 ---
    async function addToPersonalList(streamId) {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            showToast('请先登录', 'warning');
            return;
        }

        try {
            // 获取当前最大排序值
            const { data: maxOrder } = await supabase
                .from('user_streams')
                .select('sort_order')
                .eq('user_id', user.id)
                .order('sort_order', { ascending: false })
                .limit(1)
                .single();

            const nextOrder = (maxOrder?.sort_order || 0) + 1;

            const { error } = await supabase
                .from('user_streams')
                .insert({
                    user_id: user.id,
                    stream_id: streamId,
                    sort_order: nextOrder
                });

            if (error) {
                if (error.code === '23505') { // 唯一约束违反
                    showToast('该视频源已在您的列表中', 'warning');
                } else {
                    showToast('添加失败', 'error');
                }
            } else {
                showToast('已添加到个人列表', 'success');
                // 更新使用统计
                await updateStreamUsage(streamId);
                // 刷新个人列表
                loadUserPersonalStreams();
            }
        } catch (error) {
            console.error('添加到个人列表异常:', error);
            showToast('添加失败，请检查网络连接', 'error');
        }
    }

    async function updateStreamUsage(streamId) {
        try {
            const { error } = await supabase.rpc('update_stream_usage', {
                stream_id_param: streamId
            });

            if (error) {
                console.error('更新使用统计失败:', error);
            }
        } catch (error) {
            console.error('更新使用统计异常:', error);
        }
    }

    async function loadUserPersonalStreams() {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        try {
            const { data: userStreams, error } = await supabase
                .from('user_streams')
                .select(`
                    id, custom_name, sort_order, is_favorite,
                    streams!inner (
                        id, name, url, channel, quality, status
                    )
                `)
                .eq('user_id', user.id)
                .in('streams.status', ['active', 'pending']) // 显示正常和待审核的源
                .order('sort_order', { ascending: true });

            if (error) {
                console.error('加载个人列表失败:', error);
                return;
            }

            displayPersonalStreams(userStreams || []);

        } catch (error) {
            console.error('加载个人列表异常:', error);
        }
    }

    function displayPersonalStreams(userStreams) {
        // 按频道分组显示
        const channelGroups = groupStreamsByChannel(userStreams);
        displayPersonalChannels(channelGroups);
    }

    function groupStreamsByChannel(userStreams) {
        const groups = {};
        userStreams.forEach(userStream => {
            const channel = userStream.streams.channel || '其他';
            if (!groups[channel]) {
                groups[channel] = [];
            }
            groups[channel].push({
                ...userStream,
                displayName: userStream.custom_name || userStream.streams.name
            });
        });
        return groups;
    }

    function displayPersonalChannels(channelGroups) {
        const channelTabs = document.getElementById('channel-tabs');
        const sourcesList = document.getElementById('sources-list');

        if (!channelTabs || !sourcesList) return;

        // 显示频道标签
        const channels = Object.keys(channelGroups);
        channelTabs.innerHTML = channels.map((channel, index) => `
            <button class="channel-tab ${index === 0 ? 'active' : ''}"
                    onclick="switchPersonalChannel('${channel}', this)">
                ${channel}
            </button>
        `).join('');

        // 显示第一个频道的源
        if (channels.length > 0) {
            displayPersonalSources(channelGroups[channels[0]]);
        }
    }

    function switchPersonalChannel(channel, tabElement) {
        // 更新标签状态
        document.querySelectorAll('.channel-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        tabElement.classList.add('active');

        // 重新加载个人列表以获取该频道的源
        loadUserPersonalStreams();
    }

    function displayPersonalSources(sources) {
        const sourcesList = document.getElementById('sources-list');
        if (!sourcesList) return;

        sourcesList.innerHTML = sources.map(source => `
            <div class="source-item" data-user-stream-id="${source.id}" draggable="true">
                <div class="drag-handle">⋮⋮</div>
                <div class="source-info">
                    <input type="text" class="custom-name" value="${source.displayName}"
                           onchange="updateCustomName(${source.id}, this.value)">
                    <div class="source-quality">${source.streams.quality || '未知'}</div>
                </div>
                <div class="source-actions">
                    <button onclick="testPersonalStream(${source.streams.id})">测试</button>
                    <button onclick="playPersonalStream(${source.streams.id}, '${source.streams.url}')">播放</button>
                    <button onclick="toggleFavorite(${source.id})" class="${source.is_favorite ? 'favorited' : ''}">
                        ${source.is_favorite ? '⭐' : '☆'}
                    </button>
                    <button onclick="removeFromPersonalList(${source.id})">删除</button>
                </div>
            </div>
        `).join('');

        // 启用拖拽排序
        enableDragSort();
    }

    // --- 管理员功能 ---
    async function loadAdminStreams(status = '', search = '') {
        const { data: { user } } = await supabase.auth.getUser();
        if (!isAdmin(user)) {
            showToast('权限不足', 'error');
            return;
        }

        try {
            let query = supabase
                .from('streams')
                .select(`
                    id, name, url, channel, quality, status, created_at, usage_count, submitter_id
                `)
                .order('created_at', { ascending: false });

            if (status) {
                query = query.eq('status', status);
            }

            if (search) {
                query = query.ilike('name', `%${search}%`);
            }

            const { data, error } = await query;

            if (error) {
                console.error('加载管理员视频源失败:', error);
                return;
            }

            displayAdminStreams(data || []);

        } catch (error) {
            console.error('加载管理员视频源异常:', error);
        }
    }

    function displayAdminStreams(streams) {
        const container = document.getElementById('admin-streams-list');
        if (!container) return;

        container.innerHTML = streams.map(stream => `
            <div class="admin-stream-item" data-stream-id="${stream.id}">
                <div class="stream-checkbox">
                    <input type="checkbox" class="admin-stream-select" value="${stream.id}">
                </div>
                <div class="stream-info">
                    <h4>${stream.name}</h4>
                    <p class="stream-url">${stream.url}</p>
                    <div class="stream-meta">
                        <span class="tag ${stream.status}">${getStatusText(stream.status)}</span>
                        ${stream.channel ? `<span class="tag channel">${stream.channel}</span>` : ''}
                        ${stream.quality ? `<span class="tag quality">${stream.quality}</span>` : ''}
                    </div>
                    <small>
                        提交时间: ${formatDate(stream.created_at)} |
                        使用次数: ${stream.usage_count || 0}
                    </small>
                </div>
                <div class="admin-actions">
                    ${stream.status === 'pending' ? `
                        <button onclick="approveStream(${stream.id})" class="approve-btn">通过</button>
                        <button onclick="rejectStream(${stream.id})" class="reject-btn">拒绝</button>
                    ` : ''}
                    ${stream.status === 'active' ? `
                        <button onclick="suspendStream(${stream.id})" class="suspend-btn">下架</button>
                    ` : ''}
                    ${stream.status === 'rejected' ? `
                        <button onclick="restoreStream(${stream.id})" class="restore-btn">上架</button>
                    ` : ''}
                    <button onclick="deleteStream(${stream.id})" class="delete-btn">删除</button>
                    <button onclick="viewStreamReports(${stream.id})" class="reports-btn">查看举报</button>
                </div>
            </div>
        `).join('');
    }

    function getStatusText(status) {
        const statusMap = {
            'active': '正常',
            'pending': '待审核',
            'deleted': '已删除',
            'rejected': '已拒绝'
        };
        return statusMap[status] || status;
    }

    // 管理员操作函数
    async function approveStream(streamId) {
        await updateStreamStatus(streamId, 'active', 'approve');
    }

    async function rejectStream(streamId) {
        await updateStreamStatus(streamId, 'rejected', 'reject');
    }

    async function suspendStream(streamId) {
        await updateStreamStatus(streamId, 'pending', 'suspend');
    }

    async function restoreStream(streamId) {
        await updateStreamStatus(streamId, 'active', 'restore');
    }

    async function deleteStream(streamId) {
        if (!confirm('确定要删除这个视频源吗？删除后无法恢复！')) {
            return;
        }
        await updateStreamStatus(streamId, 'deleted', 'delete');
    }

    async function updateStreamStatus(streamId, newStatus, action) {
        const { data: { user } } = await supabase.auth.getUser();
        if (!isAdmin(user)) {
            showToast('权限不足', 'error');
            return;
        }

        try {
            // 更新视频源状态
            const { error: updateError } = await supabase
                .from('streams')
                .update({
                    status: newStatus,
                    ...(newStatus === 'deleted' ? {
                        deleted_at: new Date().toISOString(),
                        deleted_by: user.id
                    } : {})
                })
                .eq('id', streamId);

            if (updateError) {
                showToast('操作失败', 'error');
                return;
            }

            // 记录操作日志
            await supabase
                .from('admin_logs')
                .insert({
                    admin_id: user.id,
                    action: action,
                    target_type: 'stream',
                    target_id: streamId
                });

            showToast('操作成功', 'success');
            loadAdminStreams(); // 刷新列表

        } catch (error) {
            console.error('操作异常:', error);
            showToast('操作失败', 'error');
        }
    }

    // --- F1 Info Modal Logic ---
    function renderTable(containerId, headers, data) {
        const container = document.getElementById(containerId);
        if (!container) return;
        let tableHtml = '<table><thead><tr>';
        headers.forEach(h => tableHtml += `<th>${h}</th>`);
        tableHtml += '</tr></thead><tbody>';
        data.forEach(row => {
            tableHtml += '<tr>';
            Object.values(row).forEach(val => tableHtml += `<td>${val}</td>`);
            tableHtml += '</tr>';
        });
        tableHtml += '</tbody></table>';
        container.innerHTML = tableHtml;
    }

    async function fetchF1Data() {
        try {
            const tables = ['schedule', 'driver_standings', 'constructor_standings'];
            const [scheduleData, driverData, constructorData] = await Promise.all(
                tables.map(table => supabase.from(table).select('*'))
            );
            if (scheduleData.data) renderTable('schedule-content', ['赛事', '地点', '日期', '时间'], scheduleData.data.map(r => ({ name: r.race_name, loc: r.location, date: r.race_date, time: r.race_time })));
            if (driverData.data) renderTable('driver-standings-content', ['排名', '车手', '车队', '积分'], driverData.data.map(r => ({ pos: r.position, name: r.driver_name, team: r.team_name, pts: r.points })).sort((a, b) => a.pos - b.pos));
            if (constructorData.data) renderTable('constructor-standings-content', ['排名', '车队', '积分'], constructorData.data.map(r => ({ pos: r.position, team: r.team_name, pts: r.points })).sort((a, b) => a.pos - b.pos));
        } catch (e) {
            console.error("获取F1数据失败: ", e);
        }
    }

    // --- 模态框处理 ---
    function showScheduleModal() {
        const modal = getScheduleModal();
        if (modal) {
            modal.style.display = 'flex';
            fetchF1Data();
        }
    }

    function showStandingsModal() {
        const modal = getStandingsModal();
        if (modal) {
            modal.style.display = 'flex';
            fetchF1Data();
        }
    }

    function closeModal(modal) {
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // --- 预览和播放功能 ---
    async function previewStream(streamId) {
        const stream = await getStreamById(streamId);
        if (stream) {
            loadStream(stream.url);
            showToast(`正在预览：${stream.name}`, 'info');
        }
    }

    async function playPersonalStream(streamId, streamUrl) {
        loadStream(streamUrl);
        await updateStreamUsage(streamId);
        showToast('正在播放', 'success');
    }

    // --- 辅助功能 ---
    async function updateCustomName(userStreamId, newName) {
        try {
            const { error } = await supabase
                .from('user_streams')
                .update({ custom_name: newName })
                .eq('id', userStreamId);

            if (error) {
                console.error('更新自定义名称失败:', error);
                showToast('更新失败', 'error');
            }
        } catch (error) {
            console.error('更新自定义名称异常:', error);
        }
    }

    async function toggleFavorite(userStreamId) {
        try {
            // 先获取当前状态
            const { data: current } = await supabase
                .from('user_streams')
                .select('is_favorite')
                .eq('id', userStreamId)
                .single();

            const newFavoriteStatus = !current.is_favorite;

            const { error } = await supabase
                .from('user_streams')
                .update({ is_favorite: newFavoriteStatus })
                .eq('id', userStreamId);

            if (error) {
                console.error('更新收藏状态失败:', error);
                showToast('操作失败', 'error');
            } else {
                showToast(newFavoriteStatus ? '已收藏' : '已取消收藏', 'success');
                loadUserPersonalStreams(); // 刷新列表
            }
        } catch (error) {
            console.error('更新收藏状态异常:', error);
        }
    }

    // 删除重复的removeFromPersonalList函数，使用后面的全局版本

    function enableDragSort() {
        const sourcesList = document.getElementById('sources-list');
        if (!sourcesList || typeof Sortable === 'undefined') return;

        new Sortable(sourcesList, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            handle: '.drag-handle',
            onEnd: async function(evt) {
                const items = Array.from(sourcesList.children);
                const updates = items.map((item, index) => ({
                    id: parseInt(item.dataset.userStreamId),
                    sort_order: index
                }));

                await updateStreamOrder(updates);
            }
        });
    }

    async function updateStreamOrder(updates) {
        try {
            const { error } = await supabase
                .from('user_streams')
                .upsert(updates);

            if (error) {
                console.error('更新排序失败:', error);
                showToast('排序保存失败', 'error');
            }
        } catch (error) {
            console.error('更新排序异常:', error);
        }
    }

    // --- System Configuration ---
    let systemConfig = {
        requireInvitationCode: true // 默认需要邀请码
    };

    // --- Turnstile Configuration ---
    let turnstileToken = null;

    // Turnstile 回调函数
    window.onTurnstileSuccess = function(token) {
        turnstileToken = token;
        console.log('Turnstile 验证成功');
    };

    window.onTurnstileExpired = function() {
        turnstileToken = null;
        console.log('Turnstile 验证已过期');
    };

    window.onTurnstileError = function(error) {
        turnstileToken = null;
        console.error('Turnstile 验证错误:', error);
        showToast('人机验证失败，请刷新页面重试', 'error');
    };

    // 重置 Turnstile
    function resetTurnstile() {
        turnstileToken = null;
        if (window.turnstile) {
            const turnstileElement = document.querySelector('.cf-turnstile');
            if (turnstileElement) {
                window.turnstile.reset(turnstileElement);
            }
        }
    }

    async function loadSystemConfig() {
        try {
            const { data, error } = await supabase
                .from('system_config')
                .select('config_key, config_value')
                .in('config_key', ['require_invitation_code']);

            if (!error && data) {
                data.forEach(config => {
                    if (config.config_key === 'require_invitation_code') {
                        systemConfig.requireInvitationCode = config.config_value === 'true';
                    }
                });

                updateRegistrationUI();
            }
        } catch (error) {
            console.error('加载系统配置失败:', error);
            // 失败时使用默认配置（需要邀请码）
        }
    }

    function updateRegistrationUI() {
        const inviteCodeInput = document.getElementById('signup-invite-code');

        if (inviteCodeInput) {
            if (systemConfig.requireInvitationCode) {
                inviteCodeInput.style.display = 'block';
                inviteCodeInput.required = true;
                inviteCodeInput.placeholder = '邀请码';
            } else {
                inviteCodeInput.style.display = 'none';
                inviteCodeInput.required = false;
                inviteCodeInput.value = ''; // 清空值
            }
        }
    }

    // --- Auth Logic & Modal Handling ---
    function setupAuth() {
        const authModal = getAuthModal();
        if (!authModal) return;

        const loginView = document.getElementById('login-view');
        const signupView = document.getElementById('signup-view');
        const resetView = document.getElementById('reset-view');

        function showAuthView(view) {
            if (loginView) loginView.style.display = 'none';
            if (signupView) signupView.style.display = 'none';
            if (resetView) resetView.style.display = 'none';

            // 清空所有消息
            const loginMessage = document.getElementById('login-message');
            const signupMessage = document.getElementById('signup-message');
            const resetMessage = document.getElementById('reset-message');

            if(loginMessage) {
                loginMessage.textContent = '';
                loginMessage.className = 'auth-message';
            }
            if(signupMessage) {
                signupMessage.textContent = '';
                signupMessage.className = 'auth-message';
            }
            if(resetMessage) {
                resetMessage.textContent = '';
                resetMessage.className = 'auth-message';
            }

            // 清空表单输入
            if (view === loginView) {
                const loginEmail = document.getElementById('login-email');
                const loginPassword = document.getElementById('login-password');
                if (loginEmail) loginEmail.value = '';
                if (loginPassword) loginPassword.value = '';
            } else if (view === signupView) {
                const signupEmail = document.getElementById('signup-email');
                const signupPassword = document.getElementById('signup-password');
                const signupInviteCode = document.getElementById('signup-invite-code');
                if (signupEmail) signupEmail.value = '';
                if (signupPassword) signupPassword.value = '';
                if (signupInviteCode) signupInviteCode.value = '';
            } else if (view === resetView) {
                const resetEmail = document.getElementById('reset-email');
                if (resetEmail) resetEmail.value = '';
            }

            if (view) view.style.display = 'block';
        }

        const loginBtn = getLoginBtn();
        if(loginBtn) {
            loginBtn.addEventListener('click', () => {
                authModal.style.display = 'flex';
                showAuthView(loginView);
            });
        }

        const authCloseBtn = document.getElementById('auth-close-btn');
        if(authCloseBtn) {
            authCloseBtn.addEventListener('click', () => {
                authModal.style.display = 'none';
                // 清空所有表单和消息
                clearAllAuthForms();
            });
        }

        // 移除点击背景关闭模态框的功能，只能通过关闭按钮关闭

        // 添加清空所有认证表单的函数
        function clearAllAuthForms() {
            // 清空登录表单
            const loginEmail = document.getElementById('login-email');
            const loginPassword = document.getElementById('login-password');
            const loginMessage = document.getElementById('login-message');
            if (loginEmail) loginEmail.value = '';
            if (loginPassword) loginPassword.value = '';
            if (loginMessage) {
                loginMessage.textContent = '';
                loginMessage.className = 'auth-message';
            }

            // 清空注册表单
            const signupEmail = document.getElementById('signup-email');
            const signupPassword = document.getElementById('signup-password');
            const signupInviteCode = document.getElementById('signup-invite-code');
            const signupMessage = document.getElementById('signup-message');
            if (signupEmail) signupEmail.value = '';
            if (signupPassword) signupPassword.value = '';
            if (signupInviteCode) signupInviteCode.value = '';
            if (signupMessage) {
                signupMessage.textContent = '';
                signupMessage.className = 'auth-message';
            }

            // 清空重置密码表单
            const resetEmail = document.getElementById('reset-email');
            const resetMessage = document.getElementById('reset-message');
            if (resetEmail) resetEmail.value = '';
            if (resetMessage) {
                resetMessage.textContent = '';
                resetMessage.className = 'auth-message';
            }

            // 重置 Turnstile
            resetTurnstile();
        }

        const showSignupBtn = document.getElementById('show-signup');
        if(showSignupBtn) {
            showSignupBtn.addEventListener('click', (e) => { e.preventDefault(); showAuthView(signupView); });
        }

        const showLoginBtn = document.getElementById('show-login');
        if(showLoginBtn) {
            showLoginBtn.addEventListener('click', (e) => { e.preventDefault(); showAuthView(loginView); });
        }
        
        const showResetBtn = document.getElementById('show-reset');
        if(showResetBtn) {
            showResetBtn.addEventListener('click', (e) => { e.preventDefault(); showAuthView(resetView); });
        }

        const showLoginFromResetBtn = document.getElementById('show-login-from-reset');
        if(showLoginFromResetBtn) {
            showLoginFromResetBtn.addEventListener('click', (e) => { e.preventDefault(); showAuthView(loginView); });
        }

        const loginForm = document.getElementById('login-form');
        if(loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const email = document.getElementById('login-email').value.trim();
                const password = document.getElementById('login-password').value;
                const loginMessage = document.getElementById('login-message');

                // 前端基本验证
                if (!email) {
                    loginMessage.textContent = '请输入邮箱地址';
                    loginMessage.className = 'auth-message error';
                    return;
                }

                if (!password) {
                    loginMessage.textContent = '请输入密码';
                    loginMessage.className = 'auth-message error';
                    return;
                }

                const { data, error } = await supabase.auth.signInWithPassword({ email, password });
                if (error) {
                    const errorMsg = error.message.toLowerCase();

                    if (errorMsg.includes('invalid login credentials') || errorMsg.includes('invalid email or password')) {
                        loginMessage.textContent = '登录失败，邮箱或密码错误';
                    } else if (errorMsg.includes('email not confirmed')) {
                        loginMessage.textContent = '请先确认邮箱后再登录';
                    } else if (errorMsg.includes('too many requests') || errorMsg.includes('rate limit')) {
                        loginMessage.textContent = '登录尝试过于频繁，请稍后重试';
                    } else if (errorMsg.includes('user not found')) {
                        loginMessage.textContent = '用户不存在，请检查邮箱地址';
                    } else if (errorMsg.includes('signup disabled')) {
                        loginMessage.textContent = '账户功能已被禁用，请联系管理员';
                    } else {
                        loginMessage.textContent = `登录失败：${error.message}`;
                    }
                    loginMessage.className = 'auth-message error';
                } else {
                    showToast('登录成功！', 'success');
                    authModal.style.display = 'none';
                    clearAllAuthForms(); // 清空表单
                    updateUserUI(data.user);
                }
            });
        }

        const signupForm = document.getElementById('signup-form');
        if(signupForm) {
            signupForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const email = document.getElementById('signup-email').value.trim();
                const password = document.getElementById('signup-password').value;
                const inviteCode = document.getElementById('signup-invite-code').value.trim();
                const signupMessage = document.getElementById('signup-message');

                // 前端基本验证
                if (!email) {
                    signupMessage.textContent = '请输入邮箱地址';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                if (!email.includes('@') || !email.includes('.')) {
                    signupMessage.textContent = '请输入有效的邮箱地址';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                if (!password) {
                    signupMessage.textContent = '请输入密码';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                if (password.length < 6) {
                    signupMessage.textContent = '密码至少需要6位字符';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                // 验证 Turnstile
                if (!turnstileToken) {
                    signupMessage.textContent = '请完成人机验证';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                // 只有在需要邀请码时才验证
                if (systemConfig.requireInvitationCode && !inviteCode) {
                    signupMessage.textContent = '请输入邀请码';
                    signupMessage.className = 'auth-message error';
                    return;
                }

                signupMessage.textContent = '正在注册...';
                signupMessage.className = 'auth-message';

                try {
                    // 根据系统配置决定是否传递邀请码
                    const requestBody = { email, password, turnstile_token: turnstileToken };
                    if (systemConfig.requireInvitationCode) {
                        requestBody.invitation_code = inviteCode;
                    }

                    const { data, error } = await supabase.functions.invoke('sign-up-with-invite', {
                        body: requestBody
                    });



                    if (error) {
                        // 网络错误或函数调用失败
                        console.error('Edge Function 调用错误:', error);
                        throw new Error('注册失败，服务器连接错误，请稍后重试');
                    }

                    if (data && data.error) {
                        // 业务逻辑错误，现在应该能正确获取到了
                        const errorMsg = data.error.toLowerCase();

                        if (errorMsg.includes('无效的邀请码') || errorMsg.includes('invalid invitation')) {
                            throw new Error('注册失败，邀请码无效');
                        } else if (errorMsg.includes('已被使用') || errorMsg.includes('already used')) {
                            throw new Error('注册失败，邀请码已被使用');
                        } else if (errorMsg.includes('请输入邀请码')) {
                            throw new Error('注册失败，请输入邀请码');
                        } else if (errorMsg.includes('password') && (errorMsg.includes('short') || errorMsg.includes('weak') || errorMsg.includes('6'))) {
                            throw new Error('注册失败，密码至少需要6位字符');
                        } else if (errorMsg.includes('email') && errorMsg.includes('invalid')) {
                            throw new Error('注册失败，邮箱格式不正确');
                        } else if (errorMsg.includes('email') && (errorMsg.includes('already') || errorMsg.includes('exists'))) {
                            throw new Error('注册失败，该邮箱已被注册');
                        } else if (errorMsg.includes('user') && (errorMsg.includes('already') || errorMsg.includes('exists'))) {
                            throw new Error('注册失败，用户已存在');
                        } else if (errorMsg.includes('signup') && errorMsg.includes('disabled')) {
                            throw new Error('注册失败，注册功能已被禁用');
                        } else if (errorMsg.includes('rate limit') || errorMsg.includes('too many')) {
                            throw new Error('注册失败，请求过于频繁，请稍后重试');
                        } else {
                            // 显示原始错误信息，便于调试
                            throw new Error(`注册失败：${data.error}`);
                        }
                    }



                    // 注册成功，尝试自动登录
                    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({ email, password });
                    if (loginError) {
                        signupMessage.textContent = '注册成功，但自动登录失败，请手动登录';
                        signupMessage.className = 'auth-message warning';
                    } else {
                        showToast('注册成功！', 'success');
                        authModal.style.display = 'none';
                        clearAllAuthForms(); // 清空表单
                        updateUserUI(loginData.user);
                    }
                } catch (error) {
                    console.error('注册过程中的错误:', error);
                    signupMessage.textContent = error.message;
                    signupMessage.className = 'auth-message error';
                }
            });
        }

        const resetForm = document.getElementById('reset-form');
        if(resetForm) {
            resetForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const email = document.getElementById('reset-email').value.trim();
                const resetMessage = document.getElementById('reset-message');

                // 前端基本验证
                if (!email) {
                    resetMessage.textContent = '请输入邮箱地址';
                    resetMessage.className = 'auth-message error';
                    return;
                }

                if (!email.includes('@') || !email.includes('.')) {
                    resetMessage.textContent = '请输入有效的邮箱地址';
                    resetMessage.className = 'auth-message error';
                    return;
                }

                const { error } = await supabase.auth.resetPasswordForEmail(email, { redirectTo: window.location.origin });
                if (error) {
                    const errorMsg = error.message.toLowerCase();

                    if (errorMsg.includes('user not found') || errorMsg.includes('email not found')) {
                        resetMessage.textContent = '该邮箱地址未注册，请检查邮箱地址';
                    } else if (errorMsg.includes('rate limit') || errorMsg.includes('too many')) {
                        resetMessage.textContent = '请求过于频繁，请稍后重试';
                    } else if (errorMsg.includes('invalid email')) {
                        resetMessage.textContent = '邮箱地址格式不正确';
                    } else {
                        resetMessage.textContent = `发送失败：${error.message}`;
                    }
                    resetMessage.className = 'auth-message error';
                } else {
                    resetMessage.textContent = '重置链接已发送，请检查您的邮箱';
                    resetMessage.className = 'auth-message success';
                }
            });
        }
    }

    async function handleLogout() {
        const { error } = await supabase.auth.signOut();
        if (error) {
            showToast('登出失败，请稍后重试', 'error');
        } else {
            showToast('已成功登出', 'success');
            updateUserUI(null);
        }
    }

    // --- Admin Functions ---
    function isAdmin(user) {
        return user?.user_metadata?.role === 'admin' ||
               user?.app_metadata?.role === 'admin';
    }

    function updateUserUI(user) {
        const loginBtn = getLoginBtn();
        const userInfo = getUserInfo();
        const avatarText = document.getElementById('avatar-text');
        const dropdownEmail = document.getElementById('dropdown-email');
        const adminConfigBtn = document.getElementById('admin-config-btn');

        if (user) {
            if(loginBtn) loginBtn.style.display = 'none';
            if(userInfo) userInfo.style.display = 'flex';

            // 更新头像文字（取邮箱第一个字符）
            if(avatarText && user.email) {
                avatarText.textContent = user.email.charAt(0).toUpperCase();
            }

            // 更新下拉菜单中的邮箱
            if(dropdownEmail) {
                dropdownEmail.textContent = user.email;
            }

            // 显示/隐藏管理员配置按钮
            if(adminConfigBtn) {
                adminConfigBtn.style.display = isAdmin(user) ? 'block' : 'none';
            }
        } else {
            if(loginBtn) loginBtn.style.display = 'block';
            if(userInfo) userInfo.style.display = 'none';

            // 清空头像和邮箱信息
            if(avatarText) avatarText.textContent = 'U';
            if(dropdownEmail) dropdownEmail.textContent = '';

            // 隐藏管理员配置按钮
            if(adminConfigBtn) {
                adminConfigBtn.style.display = 'none';
            }
        }

        // 更新UI状态（新增）
        updateUIForUserState(user);
    }

    // --- 大仓库筛选辅助函数 ---
    function applyWarehouseFilters() {
        currentFilters.search = document.getElementById('search-streams').value.trim();
        currentFilters.channel = document.getElementById('channel-filter').value;
        currentFilters.quality = document.getElementById('quality-filter').value;
        currentFilters.sort = document.getElementById('sort-streams').value;

        loadWarehouseStreams(1); // 重新从第一页开始
    }

    async function testSelectedStreams() {
        console.log('=== DOMContentLoaded内部的testSelectedStreams被调用 ===');

        // 发现页面使用的是 .discovery-item-checkbox，不是 .stream-select
        const selectedCheckboxes = document.querySelectorAll('.discovery-item-checkbox:checked');
        console.log('发现页面选中的复选框数量:', selectedCheckboxes.length);

        if (selectedCheckboxes.length === 0) {
            console.log('没有选中任何复选框，显示警告');
            showGlobalToast('请先选择要测试的视频源', 'warning');
            return;
        }

        console.log('开始批量测试，选中的复选框:', selectedCheckboxes);
        showGlobalToast(`开始批量测试 ${selectedCheckboxes.length} 个视频源...`, 'info');

        // 将选中的视频源转换为ID数组
        const streamIds = Array.from(selectedCheckboxes).map(checkbox => parseInt(checkbox.value));
        console.log('要测试的视频源ID列表:', streamIds);

        // 使用并发测试，每批最多5个
        await testStreamsInBatches(streamIds, 5);

        showGlobalToast('批量测试完成', 'success');
        console.log('=== 批量测试完成 ===');
    }

    // 分批并发测试函数
    async function testStreamsInBatches(streamIds, batchSize = 5) {
        console.log(`开始分批测试，总数: ${streamIds.length}，每批: ${batchSize}`);

        for (let i = 0; i < streamIds.length; i += batchSize) {
            const batch = streamIds.slice(i, i + batchSize);
            console.log(`测试第 ${Math.floor(i / batchSize) + 1} 批，包含视频源:`, batch);

            // 并发测试当前批次的所有视频源
            const testPromises = batch.map(streamId => {
                console.log(`启动测试视频源 ${streamId}`);
                return testSingleStream(streamId);
            });

            try {
                // 等待当前批次的所有测试完成
                await Promise.all(testPromises);
                console.log(`第 ${Math.floor(i / batchSize) + 1} 批测试完成`);

                // 批次间添加短暂延迟，避免过于频繁的请求
                if (i + batchSize < streamIds.length) {
                    console.log('批次间等待 1 秒...');
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            } catch (error) {
                console.error(`第 ${Math.floor(i / batchSize) + 1} 批测试出现错误:`, error);
                // 即使有错误也继续下一批
            }
        }

        console.log('所有批次测试完成');
    }

    async function addSelectedStreams() {
        const selectedCheckboxes = document.querySelectorAll('.stream-select:checked');
        if (selectedCheckboxes.length === 0) {
            showToast('请先选择要添加的视频源', 'warning');
            return;
        }

        let successCount = 0;
        for (const checkbox of selectedCheckboxes) {
            const streamId = parseInt(checkbox.value);
            try {
                await addToPersonalList(streamId);
                successCount++;
            } catch (error) {
                console.error(`添加视频源 ${streamId} 失败:`, error);
            }
        }

        showToast(`成功添加 ${successCount} 个视频源到个人列表`, 'success');
    }

    // --- 新的发现页面筛选辅助函数 ---
    function applyDiscoveryFilters() {
        // 获取当前筛选条件
        const searchInput = document.getElementById('search-streams');
        const channelFilter = document.getElementById('channel-filter');
        const qualityFilter = document.getElementById('quality-filter');

        if (searchInput) currentFilters.search = searchInput.value.trim();
        if (channelFilter) currentFilters.channel = channelFilter.value;
        if (qualityFilter) currentFilters.quality = qualityFilter.value;

        // 重新加载发现列表
        loadDiscoveryStreams();
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 加载发现页面的流
    async function loadDiscoveryStreams() {
        const discoveryList = document.getElementById('discovery-list');
        const resultsCount = document.getElementById('results-count');

        if (!discoveryList) return;

        try {
            discoveryList.innerHTML = `
                <div class="content-loading">
                    <div class="loading-spinner"></div>
                    <span>正在搜索视频源...</span>
                </div>
            `;

            let query = globalSupabase
                .from('streams')
                .select('id, name, url, channel, quality, description, created_at, usage_count')
                .eq('status', 'active');

            // 应用快速筛选
            if (currentFilters.quickFilter) {
                switch (currentFilters.quickFilter) {
                    case 'popular':
                        query = query.order('usage_count', { ascending: false }).limit(50);
                        break;
                    case 'hd':
                        query = query.in('quality', ['4K', '1080p']);
                        break;
                    case 'stable':
                        query = query.gte('usage_count', 5);
                        break;
                    case 'recent':
                        query = query.order('created_at', { ascending: false }).limit(30);
                        break;
                }
            }

            // 应用精确筛选
            if (currentFilters.search) {
                query = query.ilike('name', `%${currentFilters.search}%`);
            }
            if (currentFilters.channel) {
                query = query.eq('channel', currentFilters.channel);
            }
            if (currentFilters.quality) {
                query = query.eq('quality', currentFilters.quality);
            }

            const { data, error } = await query;

            if (error) {
                console.error('加载发现列表失败:', error);
                discoveryList.innerHTML = '<div class="error">加载失败，请稍后重试</div>';
                return;
            }

            displayDiscoveryStreams(data || []);

            if (resultsCount) {
                resultsCount.textContent = `找到 ${data?.length || 0} 个视频源`;
            }

        } catch (error) {
            console.error('加载发现列表异常:', error);
            discoveryList.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">⚠️</div>
                    <h3>加载失败</h3>
                    <p>网络连接异常，请检查网络后重试</p>
                    <button onclick="loadDiscoveryStreams()" class="retry-btn">重试</button>
                </div>
            `;
        }
    }

    async function displayDiscoveryStreams(streams) {
        const discoveryList = document.getElementById('discovery-list');
        if (!discoveryList) return;

        if (streams.length === 0) {
            discoveryList.innerHTML = '<div class="empty">没有找到符合条件的视频源</div>';
            return;
        }

        // 获取用户已添加的视频源列表
        const userStreamIds = await getUserStreamIds();
        console.log('用户已添加的视频源ID:', userStreamIds);

        const htmlContent = streams.map(stream => {
            console.log(`生成HTML for stream ID: ${stream.id}, name: ${stream.name}`);

            // 检查是否已添加到播放列表
            const isAdded = userStreamIds.includes(stream.id);
            const addedClass = isAdded ? ' added' : '';
            const disabledAttr = isAdded ? ' disabled' : '';
            // 永远不自动勾选，让用户主动选择
            const checkedAttr = '';

            console.log(`视频源 ${stream.id} 已添加状态: ${isAdded}`);

            return `
            <div class="discovery-item${addedClass}" data-stream-id="${stream.id}">
                <input type="checkbox" class="discovery-item-checkbox" value="${stream.id}"${checkedAttr}${disabledAttr}
                       onchange="console.log('复选框变化:', ${stream.id}, this.checked); toggleStreamSelection(${stream.id}, this.checked)">
                <div class="discovery-item-info">
                    <h4 class="discovery-item-title">${stream.name}</h4>
                    <div class="discovery-item-url">${stream.url}</div>
                    <div class="discovery-item-meta">
                        ${stream.channel ? `<span class="tag channel">${stream.channel}</span>` : ''}
                        ${stream.quality ? `<span class="tag quality">${stream.quality}</span>` : ''}
                        <span class="tag usage">使用 ${stream.usage_count || 0} 次</span>
                        ${isAdded ? '<span class="tag added">已添加</span>' : ''}
                    </div>
                </div>
                <div class="discovery-item-status">
                    <div class="test-result" id="test-result-${stream.id}">未测试</div>
                </div>
                <div class="discovery-item-actions">
                    <button onclick="testSingleStream(${stream.id})" class="secondary-btn">测试</button>
                    <button onclick="previewStreamInWindow(${stream.id}, '${stream.name}', '${stream.url}')" class="secondary-btn">预览</button>
                </div>
            </div>
        `;
        }).join('');

        console.log('生成的HTML内容长度:', htmlContent.length);
        discoveryList.innerHTML = htmlContent;

        // 初始化选中状态
        await initializeSelectionState(userStreamIds);

        // 更新选择计数
        updateSelectionCount();
    }

    // 获取用户已添加的视频源ID列表
    async function getUserStreamIds() {
        try {
            const { data: { user } } = await globalSupabase.auth.getUser();
            if (!user) {
                console.log('用户未登录，无法获取已添加的视频源');
                return [];
            }

            const { data: userStreams, error } = await globalSupabase
                .from('user_streams')
                .select('stream_id')
                .eq('user_id', user.id);

            if (error) {
                console.error('获取用户视频源失败:', error);
                return [];
            }

            return userStreams.map(us => us.stream_id);
        } catch (error) {
            console.error('获取用户视频源异常:', error);
            return [];
        }
    }

    // 初始化选中状态 - 修复：不自动选中已添加的视频源
    async function initializeSelectionState(userStreamIds) {
        console.log('初始化选中状态，已添加的视频源:', userStreamIds);

        // 确保全局变量存在
        window.selectedStreams = window.selectedStreams || new Set();

        // 只更新UI显示状态，不自动添加到选择状态
        // 用户需要手动选择要操作的视频源
        userStreamIds.forEach(streamId => {
            // 更新视觉状态 - 显示为已添加但不选中
            const item = document.querySelector(`[data-stream-id="${streamId}"]`);
            const checkbox = document.querySelector(`input[value="${streamId}"]`);
            if (item) {
                item.classList.add('added'); // 使用added类而不是selected类
            }
            if (checkbox) {
                checkbox.disabled = false; // 保持可选择状态
                // 不自动勾选：checkbox.checked = false;
            }
        });

        console.log('初始化后的全局选择状态:', Array.from(window.selectedStreams));
    }

    // --- 管理员Tab切换功能 ---
    function switchAdminTab(tabName) {
        // 更新tab按钮状态
        document.querySelectorAll('.admin-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.admin-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        // 根据tab加载对应数据
        switch(tabName) {
            case 'stream-management':
                loadAdminStreams();
                break;
            case 'report-management':
                loadAdminReports();
                break;
            case 'operation-logs':
                loadAdminLogs();
                break;
        }
    }

    async function loadAdminReports() {
        // 加载举报管理数据
        try {
            const { data, error } = await supabase
                .from('stream_reports')
                .select(`
                    id, reason, created_at, stream_id, reporter_id
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('加载举报数据失败:', error);
                return;
            }

            displayAdminReports(data || []);
        } catch (error) {
            console.error('加载举报数据异常:', error);
        }
    }

    function displayAdminReports(reports) {
        const container = document.getElementById('admin-reports-list');
        if (!container) return;

        container.innerHTML = reports.map(report => `
            <div class="admin-report-item">
                <div class="report-info">
                    <h4>视频源 #${report.stream_id}</h4>
                    <p class="report-reason">举报原因: ${report.reason}</p>
                    <small>
                        举报时间: ${formatDate(report.created_at)}
                    </small>
                </div>
                <div class="report-actions">
                    <button onclick="viewReportDetails(${report.id})">查看详情</button>
                    <button onclick="dismissReport(${report.id})">忽略</button>
                </div>
            </div>
        `).join('');
    }

    async function loadAdminLogs() {
        // 加载操作日志数据
        try {
            const { data, error } = await supabase
                .from('admin_logs')
                .select(`
                    id, action, target_type, target_id, reason, created_at, admin_id
                `)
                .order('created_at', { ascending: false })
                .limit(100);

            if (error) {
                console.error('加载操作日志失败:', error);
                return;
            }

            displayAdminLogs(data || []);
        } catch (error) {
            console.error('加载操作日志异常:', error);
        }
    }

    function displayAdminLogs(logs) {
        const container = document.getElementById('admin-logs-list');
        if (!container) return;

        container.innerHTML = logs.map(log => `
            <div class="admin-log-item">
                <div class="log-info">
                    <span class="log-action">${getActionText(log.action)}</span>
                    <span class="log-target">${log.target_type} #${log.target_id}</span>
                    <small>
                        时间: ${formatDate(log.created_at)}
                        ${log.reason ? ` | 原因: ${log.reason}` : ''}
                    </small>
                </div>
            </div>
        `).join('');
    }

    function getActionText(action) {
        const actionMap = {
            'approve': '通过审核',
            'reject': '拒绝上架',
            'suspend': '下架',
            'restore': '恢复上架',
            'delete': '删除'
        };
        return actionMap[action] || action;
    }

    // --- User Avatar Dropdown Logic ---
    function setupUserDropdown() {
        const userAvatar = document.getElementById('user-avatar');
        const userDropdown = document.getElementById('user-dropdown');
        const changePasswordBtn = document.getElementById('change-password-btn');
        const changePasswordModal = document.getElementById('change-password-modal');
        const adminConfigBtn = document.getElementById('admin-config-btn');
        const adminConfigModal = document.getElementById('admin-config-modal');

        if (userAvatar && userDropdown) {
            // 点击头像切换下拉菜单
            userAvatar.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('show');
            });

            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', (e) => {
                if (!userAvatar.contains(e.target) && !userDropdown.contains(e.target)) {
                    userDropdown.classList.remove('show');
                }
            });

            // 阻止下拉菜单内部点击事件冒泡
            userDropdown.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // 修改密码按钮事件
        if (changePasswordBtn && changePasswordModal) {
            changePasswordBtn.addEventListener('click', () => {
                userDropdown.classList.remove('show'); // 关闭下拉菜单
                changePasswordModal.style.display = 'flex';
                clearChangePasswordForm();
            });
        }

        // 管理员配置按钮事件
        if (adminConfigBtn && adminConfigModal) {
            adminConfigBtn.addEventListener('click', async () => {
                userDropdown.classList.remove('show'); // 关闭下拉菜单
                adminConfigModal.style.display = 'flex';
                await loadAdminConfig(); // 加载当前配置
            });
        }
    }

    // --- Change Password Logic ---
    function clearChangePasswordForm() {
        const currentPassword = document.getElementById('current-password');
        const newPassword = document.getElementById('new-password');
        const confirmPassword = document.getElementById('confirm-password');
        const changePasswordMessage = document.getElementById('change-password-message');

        if (currentPassword) currentPassword.value = '';
        if (newPassword) newPassword.value = '';
        if (confirmPassword) confirmPassword.value = '';
        if (changePasswordMessage) {
            changePasswordMessage.textContent = '';
            changePasswordMessage.className = 'auth-message';
        }
    }

    function setupChangePassword() {
        const changePasswordModal = document.getElementById('change-password-modal');
        const changePasswordCloseBtn = document.getElementById('change-password-close-btn');
        const changePasswordForm = document.getElementById('change-password-form');
        const forgotCurrentPasswordLink = document.getElementById('forgot-current-password');
        const resetConfirmationModal = document.getElementById('reset-confirmation-modal');

        // 关闭按钮事件
        if (changePasswordCloseBtn) {
            changePasswordCloseBtn.addEventListener('click', () => {
                changePasswordModal.style.display = 'none';
                clearChangePasswordForm();
            });
        }

        // 移除点击背景关闭模态框的功能，只能通过关闭按钮关闭

        // "忘记当前密码？"链接事件
        if (forgotCurrentPasswordLink && resetConfirmationModal) {
            forgotCurrentPasswordLink.addEventListener('click', async (e) => {
                e.preventDefault();

                // 获取当前用户邮箱
                const { data: { user } } = await supabase.auth.getUser();
                if (user && user.email) {
                    const resetEmailDisplay = document.getElementById('reset-email-display');
                    if (resetEmailDisplay) {
                        resetEmailDisplay.textContent = user.email;
                    }

                    // 显示重置确认对话框
                    resetConfirmationModal.style.display = 'flex';
                    clearResetConfirmationForm();
                } else {
                    showToast('获取用户信息失败，请重新登录', 'error');
                }
            });
        }

        // 修改密码表单提交
        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                const currentPassword = document.getElementById('current-password').value;
                const newPassword = document.getElementById('new-password').value.trim();
                const confirmPassword = document.getElementById('confirm-password').value.trim();
                const changePasswordMessage = document.getElementById('change-password-message');

                // 前端验证
                if (!currentPassword) {
                    changePasswordMessage.textContent = '请输入当前密码';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                if (!newPassword) {
                    changePasswordMessage.textContent = '请输入新密码';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                if (newPassword.length < 6) {
                    changePasswordMessage.textContent = '新密码至少需要6位字符';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                if (newPassword !== confirmPassword) {
                    changePasswordMessage.textContent = '两次输入的密码不一致';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                if (currentPassword === newPassword) {
                    changePasswordMessage.textContent = '新密码不能与当前密码相同';
                    changePasswordMessage.className = 'auth-message error';
                    return;
                }

                changePasswordMessage.textContent = '正在验证当前密码...';
                changePasswordMessage.className = 'auth-message';

                try {
                    // 首先获取当前用户信息
                    const { data: { user } } = await supabase.auth.getUser();
                    if (!user || !user.email) {
                        changePasswordMessage.textContent = '用户信息获取失败，请重新登录';
                        changePasswordMessage.className = 'auth-message error';
                        return;
                    }

                    // 使用当前密码重新认证来验证密码是否正确
                    const { error: signInError } = await supabase.auth.signInWithPassword({
                        email: user.email,
                        password: currentPassword
                    });

                    if (signInError) {
                        console.error('当前密码验证失败:', signInError);
                        if (signInError.message.includes('Invalid login credentials') ||
                            signInError.message.includes('Invalid email or password')) {
                            changePasswordMessage.textContent = '当前密码错误，请重新输入';
                        } else if (signInError.message.includes('Email not confirmed')) {
                            changePasswordMessage.textContent = '邮箱未确认，无法修改密码';
                        } else {
                            changePasswordMessage.textContent = '密码验证失败，请稍后重试';
                        }
                        changePasswordMessage.className = 'auth-message error';
                        return;
                    }

                    // 当前密码验证成功，开始更新密码
                    changePasswordMessage.textContent = '正在更新密码...';
                    changePasswordMessage.className = 'auth-message';

                    // 当前密码验证成功，现在更新密码
                    const { error: updateError } = await supabase.auth.updateUser({
                        password: newPassword
                    });

                    if (updateError) {
                        if (updateError.message.includes('New password should be different')) {
                            changePasswordMessage.textContent = '新密码不能与当前密码相同';
                        } else if (updateError.message.includes('Password should be at least')) {
                            changePasswordMessage.textContent = '密码至少需要6位字符';
                        } else {
                            changePasswordMessage.textContent = '修改密码失败，请稍后重试';
                        }
                        changePasswordMessage.className = 'auth-message error';
                    } else {
                        showToast('密码修改成功！', 'success');
                        changePasswordModal.style.display = 'none';
                        clearChangePasswordForm();

                        // 密码修改成功后，确保用户界面状态正确
                        const { data: { user: updatedUser } } = await supabase.auth.getUser();
                        updateUserUI(updatedUser);
                    }
                } catch (error) {
                    console.error('修改密码错误:', error);
                    changePasswordMessage.textContent = '修改密码失败，请稍后重试';
                    changePasswordMessage.className = 'auth-message error';
                }
            });
        }

        // 设置重置密码确认对话框
        setupResetConfirmation();
    }

    // --- Reset Password Confirmation Logic ---
    function clearResetConfirmationForm() {
        const resetConfirmationMessage = document.getElementById('reset-confirmation-message');
        if (resetConfirmationMessage) {
            resetConfirmationMessage.textContent = '';
            resetConfirmationMessage.className = 'auth-message';
        }
    }

    function setupResetConfirmation() {
        const resetConfirmationModal = document.getElementById('reset-confirmation-modal');
        const resetConfirmationCloseBtn = document.getElementById('reset-confirmation-close-btn');
        const cancelResetBtn = document.getElementById('cancel-reset-btn');
        const confirmResetBtn = document.getElementById('confirm-reset-btn');

        // 关闭按钮事件
        if (resetConfirmationCloseBtn) {
            resetConfirmationCloseBtn.addEventListener('click', () => {
                resetConfirmationModal.style.display = 'none';
                clearResetConfirmationForm();
            });
        }

        // 取消按钮事件
        if (cancelResetBtn) {
            cancelResetBtn.addEventListener('click', () => {
                resetConfirmationModal.style.display = 'none';
                clearResetConfirmationForm();
            });
        }

        // 确认重置按钮事件
        if (confirmResetBtn) {
            confirmResetBtn.addEventListener('click', async () => {
                const resetConfirmationMessage = document.getElementById('reset-confirmation-message');

                try {
                    // 获取当前用户邮箱
                    const { data: { user } } = await supabase.auth.getUser();
                    if (!user || !user.email) {
                        resetConfirmationMessage.textContent = '获取用户信息失败，请重新登录';
                        resetConfirmationMessage.className = 'auth-message error';
                        return;
                    }

                    resetConfirmationMessage.textContent = '正在发送重置邮件...';
                    resetConfirmationMessage.className = 'auth-message';

                    // 发送重置密码邮件
                    const { error } = await supabase.auth.resetPasswordForEmail(user.email, {
                        redirectTo: window.location.origin
                    });

                    if (error) {
                        const errorMsg = error.message.toLowerCase();

                        if (errorMsg.includes('user not found') || errorMsg.includes('email not found')) {
                            resetConfirmationMessage.textContent = '该邮箱地址未注册';
                        } else if (errorMsg.includes('rate limit') || errorMsg.includes('too many')) {
                            resetConfirmationMessage.textContent = '请求过于频繁，请稍后重试';
                        } else {
                            resetConfirmationMessage.textContent = `发送重置邮件失败：${error.message}`;
                        }
                        resetConfirmationMessage.className = 'auth-message error';
                    } else {
                        resetConfirmationMessage.textContent = '重置邮件已发送！请检查您的邮箱并点击重置链接。';
                        resetConfirmationMessage.className = 'auth-message success';

                        // 3秒后关闭对话框
                        setTimeout(() => {
                            resetConfirmationModal.style.display = 'none';
                            clearResetConfirmationForm();

                            // 同时关闭修改密码弹窗
                            const changePasswordModal = document.getElementById('change-password-modal');
                            if (changePasswordModal) {
                                changePasswordModal.style.display = 'none';
                                clearChangePasswordForm();
                            }

                            showToast('重置邮件已发送，请检查邮箱', 'success');
                        }, 3000);
                    }
                } catch (error) {
                    console.error('重置密码错误:', error);
                    resetConfirmationMessage.textContent = '发送重置邮件失败，请稍后重试';
                    resetConfirmationMessage.className = 'auth-message error';
                }
            });
        }
    }

    // --- Admin Configuration Logic ---
    async function loadAdminConfig() {
        const requireInvitationToggle = document.getElementById('require-invitation-toggle');
        const adminConfigMessage = document.getElementById('admin-config-message');

        try {
            adminConfigMessage.textContent = '正在加载配置...';
            adminConfigMessage.className = 'auth-message';

            const { data, error } = await supabase
                .from('system_config')
                .select('config_key, config_value')
                .eq('config_key', 'require_invitation_code')
                .single();

            if (error) {
                console.error('加载管理员配置失败:', error);
                adminConfigMessage.textContent = '加载配置失败，请稍后重试';
                adminConfigMessage.className = 'auth-message error';
                return;
            }

            // 设置复选框状态
            if (requireInvitationToggle) {
                requireInvitationToggle.checked = data.config_value === 'true';
            }

            adminConfigMessage.textContent = '';
            adminConfigMessage.className = 'auth-message';

        } catch (error) {
            console.error('加载管理员配置错误:', error);
            adminConfigMessage.textContent = '加载配置失败，请稍后重试';
            adminConfigMessage.className = 'auth-message error';
        }
    }

    function clearAdminConfigForm() {
        const adminConfigMessage = document.getElementById('admin-config-message');
        if (adminConfigMessage) {
            adminConfigMessage.textContent = '';
            adminConfigMessage.className = 'auth-message';
        }
    }

    function setupAdminConfig() {
        const adminConfigModal = document.getElementById('admin-config-modal');
        const adminConfigCloseBtn = document.getElementById('admin-config-close-btn');
        const saveConfigBtn = document.getElementById('save-config-btn');

        // 关闭按钮事件
        if (adminConfigCloseBtn) {
            adminConfigCloseBtn.addEventListener('click', () => {
                adminConfigModal.style.display = 'none';
                clearAdminConfigForm();
            });
        }

        // 管理员Tab切换
        const adminTabs = document.querySelectorAll('.admin-tab');
        adminTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.getAttribute('data-tab');
                switchAdminTab(tabName);
            });
        });

        // 管理员搜索功能
        const adminSearchBtn = document.getElementById('admin-search-btn');
        const adminStatusFilter = document.getElementById('admin-status-filter');
        if(adminSearchBtn) {
            adminSearchBtn.addEventListener('click', () => {
                const search = document.getElementById('admin-search').value.trim();
                const status = adminStatusFilter.value;
                loadAdminStreams(status, search);
            });
        }

        // 保存配置按钮事件
        if (saveConfigBtn) {
            saveConfigBtn.addEventListener('click', async () => {
                const requireInvitationToggle = document.getElementById('require-invitation-toggle');
                const adminConfigMessage = document.getElementById('admin-config-message');

                if (!requireInvitationToggle) return;

                try {
                    adminConfigMessage.textContent = '正在保存配置...';
                    adminConfigMessage.className = 'auth-message';

                    const newValue = requireInvitationToggle.checked ? 'true' : 'false';

                    const { error } = await supabase
                        .from('system_config')
                        .update({
                            config_value: newValue,
                            updated_at: new Date().toISOString()
                        })
                        .eq('config_key', 'require_invitation_code');

                    if (error) {
                        console.error('保存配置失败:', error);
                        if (error.message.includes('permission') || error.message.includes('policy') || error.message.includes('RLS')) {
                            adminConfigMessage.textContent = '权限不足，无法修改配置。请检查管理员权限设置。';
                        } else {
                            adminConfigMessage.textContent = `保存配置失败：${error.message}`;
                        }
                        adminConfigMessage.className = 'auth-message error';
                        return;
                    }

                    // 更新本地配置
                    systemConfig.requireInvitationCode = requireInvitationToggle.checked;
                    updateRegistrationUI();

                    adminConfigMessage.textContent = '配置保存成功！';
                    adminConfigMessage.className = 'auth-message success';

                    showToast('系统配置已更新', 'success');

                    // 2秒后关闭模态框
                    setTimeout(() => {
                        adminConfigModal.style.display = 'none';
                        clearAdminConfigForm();
                    }, 2000);

                } catch (error) {
                    console.error('保存配置错误:', error);
                    adminConfigMessage.textContent = '保存配置失败，请稍后重试';
                    adminConfigMessage.className = 'auth-message error';
                }
            });
        }
    }

    // --- General Event Listeners ---
    function setupEventListeners() {
        const loadStreamBtn = getLoadStreamBtn();
        if(loadStreamBtn) {
            loadStreamBtn.addEventListener('click', () => {
                const m3uInput = getM3uInput();
                if (m3uInput) loadStream(m3uInput.value.trim());
            });
        }
        
        const m3uInput = getM3uInput();
        if(m3uInput) m3uInput.addEventListener('keypress', e => { if (e.key === 'Enter') loadStream(m3uInput.value.trim()); });
        
        const logoutBtn = getLogoutBtn();
        if(logoutBtn) logoutBtn.addEventListener('click', handleLogout);

        const sendDanmakuBtn = getSendDanmakuBtn();
        if(sendDanmakuBtn) sendDanmakuBtn.addEventListener('click', sendDanmaku);

        const danmakuInput = getDanmakuInput();
        if(danmakuInput) danmakuInput.addEventListener('keypress', e => { if (e.key === 'Enter') sendDanmaku(); });

        const contributeBtn = getContributeBtn();
        if(contributeBtn) contributeBtn.addEventListener('click', handleContributeStream);

        const streamDropdown = getStreamDropdown();
        if(streamDropdown) {
            streamDropdown.addEventListener('change', () => {
                const selectedUrl = streamDropdown.value;
                if (selectedUrl) {
                    const m3uInput = getM3uInput();
                    if(m3uInput) m3uInput.value = selectedUrl;
                    loadStream(selectedUrl);
                }
            });
        }

        // 赛程按钮
        const scheduleBtn = getScheduleBtn();
        const scheduleModal = getScheduleModal();
        if(scheduleBtn && scheduleModal) {
            scheduleBtn.addEventListener('click', showScheduleModal);
            const scheduleModalCloseBtn = scheduleModal.querySelector('.auth-close-btn');
            if(scheduleModalCloseBtn) scheduleModalCloseBtn.addEventListener('click', () => closeModal(scheduleModal));
        }

        // 积分榜按钮
        const standingsBtn = getStandingsBtn();
        const standingsModal = getStandingsModal();
        if(standingsBtn && standingsModal) {
            standingsBtn.addEventListener('click', showStandingsModal);
            const standingsModalCloseBtn = standingsModal.querySelector('.auth-close-btn');
            if(standingsModalCloseBtn) standingsModalCloseBtn.addEventListener('click', () => closeModal(standingsModal));
        }

        // 筛选视频源按钮（现在是发现页面）
        const filterSourcesBtn = getFilterSourcesBtn();
        const discoveryModal = document.getElementById('discovery-modal');
        if(filterSourcesBtn && discoveryModal) {
            filterSourcesBtn.addEventListener('click', showDiscoveryModal);
            const discoveryModalCloseBtn = discoveryModal.querySelector('.auth-close-btn');
            if(discoveryModalCloseBtn) discoveryModalCloseBtn.addEventListener('click', closeDiscoveryModal);

            // 发现页面功能按钮
            const searchBtn = document.getElementById('search-btn');
            const selectAllBtn = document.getElementById('select-all-btn');
            const testSelectedBtn = document.getElementById('test-selected-btn');
            const continueToManageBtn = document.getElementById('continue-to-manage-btn');

            if(searchBtn) searchBtn.addEventListener('click', applyDiscoveryFilters);
            if(selectAllBtn) selectAllBtn.addEventListener('click', selectAllStreams);
            if(testSelectedBtn) testSelectedBtn.addEventListener('click', testSelectedStreams);
            if(continueToManageBtn) continueToManageBtn.addEventListener('click', continueToManage);

            // 筛选条件变化监听
            const channelFilter = document.getElementById('channel-filter');
            const qualityFilter = document.getElementById('quality-filter');
            const searchStreams = document.getElementById('search-streams');

            if(channelFilter) channelFilter.addEventListener('change', applyDiscoveryFilters);
            if(qualityFilter) qualityFilter.addEventListener('change', applyDiscoveryFilters);
            if(searchStreams) searchStreams.addEventListener('input', debounce(applyDiscoveryFilters, 500));

            // 个人管理页面按钮
            const personalModal = document.getElementById('personal-modal');
            if(personalModal) {
                const personalModalCloseBtn = personalModal.querySelector('.auth-close-btn');
                const backToDiscoveryBtn = document.getElementById('back-to-discovery-btn');
                const backToDiscoveryBtn2 = document.getElementById('back-to-discovery-btn-2');
                const saveAndPlayBtn = document.getElementById('save-and-play-btn');

                if(personalModalCloseBtn) personalModalCloseBtn.addEventListener('click', () => personalModal.style.display = 'none');
                if(backToDiscoveryBtn) backToDiscoveryBtn.addEventListener('click', backToDiscovery);
                if(backToDiscoveryBtn2) backToDiscoveryBtn2.addEventListener('click', backToDiscovery);
                if(saveAndPlayBtn) saveAndPlayBtn.addEventListener('click', saveAndPlay);
            }
        }

        // 提交视频源模态框
        const contributeModal = getContributeModal();
        if(contributeModal) {
            const contributeModalCloseBtn = contributeModal.querySelector('.auth-close-btn');
            if(contributeModalCloseBtn) contributeModalCloseBtn.addEventListener('click', closeContributeModal);

            const testBeforeSubmitBtn = document.getElementById('test-before-submit');
            const contributeForm = document.getElementById('contribute-form');

            if(testBeforeSubmitBtn) testBeforeSubmitBtn.addEventListener('click', testStreamBeforeSubmit);
            if(contributeForm) contributeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                submitStreamToWarehouse();
            });
        }

        // 举报模态框
        const reportModal = getReportModal();
        if(reportModal) {
            const reportModalCloseBtn = reportModal.querySelector('.auth-close-btn');
            const cancelReportBtn = document.getElementById('cancel-report-btn');
            const reportForm = document.getElementById('report-form');

            if(reportModalCloseBtn) reportModalCloseBtn.addEventListener('click', closeReportModal);
            if(cancelReportBtn) cancelReportBtn.addEventListener('click', closeReportModal);
            if(reportForm) reportForm.addEventListener('submit', (e) => {
                e.preventDefault();
                submitReport();
            });
        }

        // 聊天室功能
        const chatCloseBtn = getChatCloseBtn();
        const chatFloat = getChatFloat();
        if(chatCloseBtn) chatCloseBtn.addEventListener('click', closeChat);
        if(chatFloat) chatFloat.addEventListener('click', openChat);

        const sendChatBtn = getSendChatBtn();
        if(sendChatBtn) sendChatBtn.addEventListener('click', sendChatMessage);

        const chatInput = getChatInput();
        if(chatInput) chatInput.addEventListener('keypress', e => { if (e.key === 'Enter') sendChatMessage(); });

        // Tab切换功能
        const channelTab = document.getElementById('channel-tab');
        const manualTab = document.getElementById('manual-tab');
        if(channelTab) channelTab.addEventListener('click', () => switchSourceTab('channel'));
        if(manualTab) manualTab.addEventListener('click', () => switchSourceTab('manual'));

        // 登录用户的手动输入功能
        const loadStreamBtnLogged = document.getElementById('load-stream-btn-logged');
        const m3uInputLogged = document.getElementById('m3u-input-logged');
        if(loadStreamBtnLogged && m3uInputLogged) {
            loadStreamBtnLogged.addEventListener('click', () => {
                loadStream(m3uInputLogged.value.trim());
            });
            m3uInputLogged.addEventListener('keypress', e => {
                if (e.key === 'Enter') loadStream(m3uInputLogged.value.trim());
            });
        }

        const modalTabs = document.querySelector('.modal-tabs');
        if(modalTabs) {
            modalTabs.addEventListener('click', e => {
                if (e.target.tagName === 'BUTTON') {
                    const activeTab = document.querySelector('.tab-link.active');
                    if(activeTab) activeTab.classList.remove('active');
                    const activeContent = document.querySelector('.tab-content.active');
                    if(activeContent) activeContent.classList.remove('active');
                    e.target.classList.add('active');
                    const tabContent = document.getElementById(e.target.dataset.tab);
                    if(tabContent) tabContent.classList.add('active');
                }
            });
        }

        const videoPlayer = getVideoPlayer();
        if(videoPlayer) {
            videoPlayer.addEventListener('dblclick', () => { 
                if (!document.fullscreenElement) videoPlayer.requestFullscreen(); 
                else document.exitFullscreen(); 
            });
        }

        document.addEventListener('keydown', e => {
            if (e.target.tagName.toLowerCase() === 'input') return;
            const video = getVideoPlayer();
            if(!video) return;
            switch (e.code) {
                case 'Space': e.preventDefault(); video.paused ? video.play() : video.pause(); break;
                case 'KeyF': e.preventDefault(); if (!document.fullscreenElement) video.requestFullscreen(); else document.exitFullscreen(); break;
                case 'ArrowUp': e.preventDefault(); video.volume = Math.min(1, video.volume + 0.1); break;
                case 'ArrowDown': e.preventDefault(); video.volume = Math.max(0, video.volume - 0.1); break;
                case 'ArrowRight': e.preventDefault(); video.currentTime += 5; break;
                case 'ArrowLeft': e.preventDefault(); video.currentTime -= 5; break;
            }
        });
    }

    // --- Initial Load ---
    async function initializeApp() {
        try {
            const { data: { session } } = await supabase.auth.getSession();
            updateUserUI(session?.user ?? null);
            supabase.auth.onAuthStateChange((_event, session) => updateUserUI(session?.user ?? null));
            
            const lastUrl = localStorage.getItem('lastStreamUrl');
            const m3uInput = getM3uInput();
            if (lastUrl && m3uInput) {
                m3uInput.value = lastUrl;
            }

            // 首先加载系统配置
            await loadSystemConfig();

            setupAuth();
            setupEventListeners();
            setupUserDropdown();
            setupChangePassword();
            setupAdminConfig();
            subscribeToDanmaku();
            fetchAndDisplayStreams();
        } catch (e) {
            console.error("初始化应用失败: ", e);
            showToast("应用加载失败，请检查控制台错误信息", 'error');
        }
    }

    initializeApp();
});

// --- 全局函数（供HTML onclick调用） ---
// 这些函数需要在全局作用域中，以便HTML的onclick属性可以访问

// 获取Supabase客户端（全局访问）
const SUPABASE_URL = 'https://znidotclrjksnizomrmh.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpuaWRvdGNscmprc25pem9tcm1oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQzMjM0OTYsImV4cCI6MjA2OTg5OTQ5Nn0.16oxptca7i9l_kO6mvAlc8ProhQ5UfhQ9RToShzvm5M';
const globalSupabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// 全局选择状态
window.selectedStreams = new Set();

// 全局Toast函数
function showGlobalToast(message, type = 'info') {
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 全局测试函数（供HTML onclick调用）
async function testSingleStream(streamId, retryCount = 0) {
    const maxRetries = 3;
    const testResult = document.getElementById(`test-result-${streamId}`);
    const testProgress = document.getElementById(`test-progress-${streamId}`);

    if (!testResult) {
        console.error(`找不到测试结果元素: test-result-${streamId}`);
        return;
    }

    testResult.textContent = '测试中...';
    testResult.className = 'test-result testing';
    if (testProgress) testProgress.style.display = 'block';

    try {
        const stream = await getStreamById(streamId);
        if (!stream) {
            throw new Error('视频源不存在');
        }

        const result = await testStreamConnection(stream.url);

        testResult.textContent = `✅ 可用 (${result.loadTime}ms)`;
        testResult.className = 'test-result success';

    } catch (error) {
        console.error(`测试视频源 ${streamId} 失败:`, error);
        if (retryCount < maxRetries - 1) {
            testResult.textContent = `重试中... (${retryCount + 1}/${maxRetries})`;
            setTimeout(() => testSingleStream(streamId, retryCount + 1), 1000);
        } else {
            testResult.textContent = '❌ 失效';
            testResult.className = 'test-result failed';
        }
    } finally {
        if (testProgress) testProgress.style.display = 'none';
    }
}

// 视频源测试连接功能
async function testStreamConnection(url) {
    return new Promise((resolve, reject) => {
        const video = document.createElement('video');
        video.style.display = 'none';
        document.body.appendChild(video);

        const startTime = Date.now();
        let timeout;

        if (Hls.isSupported()) {
            const hls = new Hls();

            hls.on(Hls.Events.MANIFEST_PARSED, () => {
                const loadTime = Date.now() - startTime;
                cleanup();
                resolve({ loadTime });
            });

            hls.on(Hls.Events.ERROR, (event, data) => {
                if (data.fatal) {
                    cleanup();
                    reject(new Error(data.details || '连接失败'));
                }
            });

            timeout = setTimeout(() => {
                cleanup();
                reject(new Error('连接超时'));
            }, 5000);

            hls.loadSource(url);
            hls.attachMedia(video);

            function cleanup() {
                clearTimeout(timeout);
                hls.destroy();
                document.body.removeChild(video);
            }
        } else {
            document.body.removeChild(video);
            reject(new Error('浏览器不支持HLS'));
        }
    });
}

// 获取视频源信息
async function getStreamById(streamId) {
    try {
        const { data, error } = await globalSupabase
            .from('streams')
            .select('*')
            .eq('id', streamId)
            .single();

        if (error) {
            console.error('获取视频源失败:', error);
            return null;
        }

        return data;
    } catch (error) {
        console.error('获取视频源异常:', error);
        return null;
    }
}

// 预览视频源
async function previewStream(streamId) {
    const stream = await getStreamById(streamId);
    if (stream) {
        // 调用全局的loadStream函数
        if (window.loadStream) {
            window.loadStream(stream.url);
            showGlobalToast(`正在预览：${stream.name}`, 'info');
        } else {
            showGlobalToast('视频播放器未初始化', 'error');
        }
    }
}

// 添加到个人列表
async function addToPersonalList(streamId) {
    const { data: { user } } = await globalSupabase.auth.getUser();
    if (!user) {
        showGlobalToast('请先登录', 'warning');
        return;
    }

    try {
        const { data: maxOrder } = await globalSupabase
            .from('user_streams')
            .select('sort_order')
            .eq('user_id', user.id)
            .order('sort_order', { ascending: false })
            .limit(1)
            .maybeSingle();

        const nextOrder = (maxOrder?.sort_order || 0) + 1;

        const { error } = await globalSupabase
            .from('user_streams')
            .insert({
                user_id: user.id,
                stream_id: streamId,
                sort_order: nextOrder
            });

        if (error) {
            if (error.code === '23505') {
                showGlobalToast('该视频源已在您的列表中', 'warning');
            } else {
                showGlobalToast('添加失败', 'error');
            }
        } else {
            showGlobalToast('已添加到个人列表', 'success');
            await updateStreamUsage(streamId);
        }
    } catch (error) {
        console.error('添加到个人列表异常:', error);
        showGlobalToast('添加失败，请检查网络连接', 'error');
    }
}

// 更新使用统计
async function updateStreamUsage(streamId) {
    try {
        const { error } = await globalSupabase.rpc('update_stream_usage', {
            stream_id_param: streamId
        });

        if (error) {
            console.error('更新使用统计失败:', error);
        }
    } catch (error) {
        console.error('更新使用统计异常:', error);
    }
}

// 显示举报模态框
function showReportModal(streamId, streamName, streamUrl) {
    document.getElementById('report-stream-name').textContent = streamName;
    document.getElementById('report-stream-url').textContent = streamUrl;

    const reportModal = document.getElementById('report-modal');
    if (reportModal) {
        reportModal.style.display = 'flex';
        // 存储当前举报的视频源ID到全局变量
        window.currentReportStreamId = streamId;
    }
}

// 全局视频加载函数
let globalHls = null;
function loadStream(sourceUrl) {
    const video = document.getElementById('video-player');
    if (!video) {
        showGlobalToast('视频播放器未找到', 'error');
        return;
    }

    if (!sourceUrl) {
        showGlobalToast('请输入有效的 M3U/M3U8 链接', 'warning');
        return;
    }

    if (Hls.isSupported()) {
        if (globalHls) globalHls.destroy();
        globalHls = new Hls();
        globalHls.loadSource(sourceUrl);
        globalHls.attachMedia(video);
        globalHls.on(Hls.Events.MANIFEST_PARSED, () => {
            video.play().catch(e => console.error("播放失败：", e));
            localStorage.setItem('lastStreamUrl', sourceUrl);
        });
        globalHls.on(Hls.Events.ERROR, (_, data) => {
            if (data.fatal) {
                console.error('HLS 加载发生致命错误:', data);
                showGlobalToast('视频源加载失败，请检查链接或更换线路', 'error');
            }
        });
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        video.src = sourceUrl;
        video.addEventListener('loadedmetadata', () => {
            video.play().catch(e => console.error("播放失败：", e));
        });
    } else {
        showGlobalToast('您的浏览器不支持 HLS 播放', 'error');
    }
}

// 暴露loadStream到全局
window.loadStream = loadStream;

// 管理员操作函数
async function approveStream(streamId) {
    await updateStreamStatus(streamId, 'active', 'approve');
}

async function rejectStream(streamId) {
    await updateStreamStatus(streamId, 'rejected', 'reject');
}

async function suspendStream(streamId) {
    await updateStreamStatus(streamId, 'pending', 'suspend');
}

async function restoreStream(streamId) {
    await updateStreamStatus(streamId, 'active', 'restore');
}

async function deleteStream(streamId) {
    if (!confirm('确定要删除这个视频源吗？删除后无法恢复！')) {
        return;
    }
    await updateStreamStatus(streamId, 'deleted', 'delete');
}

// 管理员更新视频源状态
async function updateStreamStatus(streamId, newStatus, action) {
    const { data: { user } } = await globalSupabase.auth.getUser();
    if (!isAdmin(user)) {
        showGlobalToast('权限不足', 'error');
        return;
    }

    try {
        const { error: updateError } = await globalSupabase
            .from('streams')
            .update({
                status: newStatus,
                ...(newStatus === 'deleted' ? {
                    deleted_at: new Date().toISOString(),
                    deleted_by: user.id
                } : {})
            })
            .eq('id', streamId);

        if (updateError) {
            showGlobalToast('操作失败', 'error');
            return;
        }

        await globalSupabase
            .from('admin_logs')
            .insert({
                admin_id: user.id,
                action: action,
                target_type: 'stream',
                target_id: streamId
            });

        showGlobalToast('操作成功', 'success');

    } catch (error) {
        console.error('操作异常:', error);
        showGlobalToast('操作失败', 'error');
    }
}

// 检查是否为管理员
function isAdmin(user) {
    if (!user) return false;

    const userMetadata = user.user_metadata || {};
    const appMetadata = user.app_metadata || {};

    return userMetadata.role === 'admin' ||
           appMetadata.role === 'admin' ||
           user.role === 'admin';
}

// 个人列表相关函数
async function testPersonalStream(streamId) {
    await testSingleStream(streamId);
}

async function playPersonalStream(streamId, streamUrl) {
    loadStream(streamUrl);
    await updateStreamUsage(streamId);
    showGlobalToast('正在播放', 'success');
}

async function updateCustomName(userStreamId, newName) {
    try {
        const { error } = await globalSupabase
            .from('user_streams')
            .update({ custom_name: newName })
            .eq('id', userStreamId);

        if (error) {
            console.error('更新自定义名称失败:', error);
            showGlobalToast('更新失败', 'error');
        }
    } catch (error) {
        console.error('更新自定义名称异常:', error);
    }
}

async function toggleFavorite(userStreamId) {
    try {
        const { data: current } = await globalSupabase
            .from('user_streams')
            .select('is_favorite')
            .eq('id', userStreamId)
            .single();

        const newFavoriteStatus = !current.is_favorite;

        const { error } = await globalSupabase
            .from('user_streams')
            .update({ is_favorite: newFavoriteStatus })
            .eq('id', userStreamId);

        if (error) {
            console.error('更新收藏状态失败:', error);
            showGlobalToast('操作失败', 'error');
        } else {
            showGlobalToast(newFavoriteStatus ? '已收藏' : '已取消收藏', 'success');
        }
    } catch (error) {
        console.error('更新收藏状态异常:', error);
    }
}

// 本地删除功能（不立即写数据库）
function removeFromPersonalList(userStreamId) {
    if (!confirm('确定要从个人列表中移除这个视频源吗？')) {
        return;
    }

    // 直接从DOM中移除该项
    const streamItem = document.querySelector(`[data-user-stream-id="${userStreamId}"]`);
    if (streamItem) {
        // 立即更新频道计数（在动画开始前）
        updateChannelCountOnDelete(streamItem);

        // 添加删除动画
        streamItem.style.transition = 'all 0.3s ease';
        streamItem.style.opacity = '0';
        streamItem.style.transform = 'translateX(-100%)';

        // 在删除前获取频道信息，避免删除后无法获取
        const channelStreams = streamItem.closest('.channel-streams');
        const channelGroup = channelStreams ? channelStreams.parentElement : null;
        const countElement = channelGroup ? channelGroup.querySelector('.channel-count') : null;

        console.log('setTimeout前获取的元素:');
        console.log('channelStreams:', channelStreams);
        console.log('channelGroup:', channelGroup);
        console.log('countElement:', countElement);

        setTimeout(() => {
            streamItem.remove();

            // 更新统计信息
            updatePersonalStatsFromDOM();

            // 更新频道计数和状态
            if (channelStreams && channelGroup && countElement) {
                const remainingItems = channelStreams.children.length;
                console.log(`频道计数更新: 删除后剩余 ${remainingItems} 个视频源`);

                countElement.textContent = remainingItems;

                // 如果频道为空，标记为已清空
                if (remainingItems === 0) {
                    console.log('频道已清空，标记为已清空状态');
                    channelGroup.style.opacity = '0.5';
                    const channelTitle = channelGroup.querySelector('.channel-title span:last-child');
                    if (channelTitle && !channelTitle.textContent.includes('(已清空)')) {
                        channelTitle.textContent += ' (已清空)';
                    }
                } else {
                    console.log(`频道还有 ${remainingItems} 个视频源，保持正常状态`);
                    // 确保频道不是半透明状态（如果之前被标记为已清空）
                    channelGroup.style.opacity = '1';
                    const channelTitle = channelGroup.querySelector('.channel-title span:last-child');
                    if (channelTitle && channelTitle.textContent.includes('(已清空)')) {
                        channelTitle.textContent = channelTitle.textContent.replace(' (已清空)', '');
                    }
                }
            } else {
                console.error('无法更新频道计数：找不到相关元素');
            }

            showGlobalToast('已从列表中移除（点击保存或返回时同步到数据库）', 'success');
        }, 300);
    }

    // 记录删除的项目，保存时会用到
    if (!window.deletedStreamIds) {
        window.deletedStreamIds = new Set();
    }
    window.deletedStreamIds.add(userStreamId);
}

// 立即更新频道计数（在删除动画开始前）
function updateChannelCountOnDelete(streamItem) {
    console.log('立即更新频道计数...');
    console.log('要删除的元素:', streamItem);

    // 个人管理页面的HTML结构：.personal-stream-item 在 .channel-streams 内部
    const channelStreams = streamItem.closest('.channel-streams');
    if (!channelStreams) {
        console.error('找不到频道容器 .channel-streams');
        console.log('streamItem的父级元素:', streamItem.parentElement);
        return;
    }

    console.log('找到频道容器:', channelStreams);

    // .channel-streams 的父级是 .channel-group
    const channelGroup = channelStreams.parentElement;
    if (!channelGroup || !channelGroup.classList.contains('channel-group')) {
        console.error('找不到频道组 .channel-group');
        console.log('channelStreams的父级元素:', channelStreams.parentElement);
        return;
    }

    console.log('找到频道组:', channelGroup);

    const countElement = channelGroup.querySelector('.channel-count');
    if (!countElement) {
        console.error('找不到计数元素 .channel-count');
        console.log('channelGroup内的所有元素:', channelGroup.innerHTML);
        return;
    }

    console.log('找到计数元素:', countElement);

    // 计算删除后的数量（当前数量 - 1）
    const currentCount = channelStreams.children.length;
    const newCount = currentCount - 1;

    console.log(`频道计数更新: ${currentCount} → ${newCount}`);

    // 立即更新计数显示
    countElement.textContent = newCount;

    // 如果删除后频道为空，立即标记为已清空
    if (newCount === 0) {
        console.log('频道将被清空，立即标记为已清空状态');
        channelGroup.style.opacity = '0.5';
        const channelTitle = channelGroup.querySelector('.channel-title span:last-child');
        if (channelTitle && !channelTitle.textContent.includes('(已清空)')) {
            channelTitle.textContent += ' (已清空)';
        }
    }

    console.log('频道计数更新完成');
}

// 频道切换函数
function switchPersonalChannel(channel, tabElement) {
    document.querySelectorAll('.channel-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    tabElement.classList.add('active');

    // 这里可以添加切换频道的逻辑
    showGlobalToast(`切换到 ${channel} 频道`, 'info');
}

// 举报相关函数
function viewReportDetails(reportId) {
    showGlobalToast(`查看举报详情 #${reportId}`, 'info');
}

function dismissReport(reportId) {
    if (confirm('确定要忽略这个举报吗？')) {
        showGlobalToast(`已忽略举报 #${reportId}`, 'success');
    }
}

// 新的两步式流程全局函数
function toggleStreamSelection(streamId, isSelected) {
    console.log(`=== toggleStreamSelection 被调用 ===`);
    console.log(`参数: streamId=${streamId} (类型: ${typeof streamId}), isSelected=${isSelected} (类型: ${typeof isSelected})`);

    // 确保streamId是数字
    const numericStreamId = parseInt(streamId);
    console.log(`转换后的streamId: ${numericStreamId}`);

    if (isSelected) {
        console.log('执行添加操作...');
        window.selectedStreams = window.selectedStreams || new Set();
        console.log('添加前的状态:', Array.from(window.selectedStreams));

        window.selectedStreams.add(numericStreamId);
        console.log(`添加视频源 ${numericStreamId} 完成`);
        console.log('添加后的状态:', Array.from(window.selectedStreams));
        console.log('当前Set大小:', window.selectedStreams.size);
    } else {
        console.log('执行移除操作...');
        if (window.selectedStreams) {
            console.log('移除前的状态:', Array.from(window.selectedStreams));
            window.selectedStreams.delete(numericStreamId);
            console.log(`移除视频源 ${numericStreamId} 完成`);
            console.log('移除后的状态:', Array.from(window.selectedStreams));
            console.log('当前Set大小:', window.selectedStreams.size);
        } else {
            console.log('window.selectedStreams 不存在，无法移除');
        }
    }

    console.log('调用 updateSelectionCount...');
    updateSelectionCount();

    console.log('调用 updateContinueButton...');
    updateContinueButton();

    // 更新视觉状态
    const item = document.querySelector(`[data-stream-id="${numericStreamId}"]`);
    console.log('查找视觉元素:', item);
    if (item) {
        item.classList.toggle('selected', isSelected);
        console.log('视觉状态已更新');
    } else {
        console.log(`未找到视觉元素 [data-stream-id="${numericStreamId}"]`);
    }

    console.log(`=== toggleStreamSelection 执行完成 ===`);
}

function updateSelectionCount() {
    const selectedCount = document.getElementById('selected-count');
    const count = window.selectedStreams ? window.selectedStreams.size : 0;
    console.log(`updateSelectionCount: 当前选中数量=${count}`); // 调试日志
    if (selectedCount) {
        selectedCount.textContent = `已选择 ${count} 个视频源`;
    }
}

function updateContinueButton() {
    const continueBtn = document.getElementById('continue-to-manage-btn');
    const count = window.selectedStreams ? window.selectedStreams.size : 0;
    if (continueBtn) {
        continueBtn.disabled = count === 0;
    }
}

// 修复后的全选功能 - 智能跳过已添加的视频源
function selectAllStreams() {
    console.log('=== 开始全选/取消全选功能 ===');

    const checkboxes = document.querySelectorAll('.discovery-item-checkbox:not(:disabled)');
    console.log('找到可选择的复选框数量:', checkboxes.length);

    if (checkboxes.length === 0) {
        console.log('没有找到任何可选择的复选框');
        return;
    }

    // 只检查未添加的视频源是否全部选中
    const availableCheckboxes = Array.from(checkboxes).filter(cb => {
        const item = document.querySelector(`[data-stream-id="${cb.value}"]`);
        return !item || !item.classList.contains('added');
    });

    const allAvailableSelected = availableCheckboxes.every(cb => cb.checked);
    const shouldSelect = !allAvailableSelected;

    console.log('可选择的复选框数量:', availableCheckboxes.length);
    console.log('当前全部选中状态:', allAvailableSelected);
    console.log('将要设置为:', shouldSelect ? '全选' : '取消全选');

    // 确保全局变量存在
    window.selectedStreams = window.selectedStreams || new Set();

    // 只操作未添加的视频源
    availableCheckboxes.forEach((checkbox, index) => {
        const streamId = parseInt(checkbox.value);
        const item = document.querySelector(`[data-stream-id="${streamId}"]`);

        // 跳过已添加的视频源
        if (item && item.classList.contains('added')) {
            console.log(`跳过已添加的视频源 ID: ${streamId}`);
            return;
        }

        console.log(`处理第 ${index + 1} 个复选框，ID: ${streamId}, 设置为: ${shouldSelect}`);

        // 更新复选框状态
        checkbox.checked = shouldSelect;

        // 更新全局状态
        if (shouldSelect) {
            window.selectedStreams.add(streamId);
        } else {
            window.selectedStreams.delete(streamId);
        }

        // 更新视觉状态
        if (item) {
            item.classList.toggle('selected', shouldSelect);
        }
    });

    // 统一更新UI
    updateSelectionCount();
    updateContinueButton();

    console.log('更新后的全局选择状态:', window.selectedStreams ? Array.from(window.selectedStreams) : []);

    // 更新全选按钮文字
    const selectAllBtn = document.getElementById('select-all-btn');
    if (selectAllBtn) {
        const newText = shouldSelect ? '取消全选' : '全选可用源';
        selectAllBtn.textContent = newText;
        console.log('更新全选按钮文字为:', newText);
    }

    console.log('=== 全选/取消全选功能完成 ===');
}

// 全新的批量测试功能（支持并发）
async function testSelectedStreams() {
    console.log('=== 开始批量测试功能（全局版本）===');

    // 方法1：直接从复选框获取选中的视频源
    const checkedBoxes = document.querySelectorAll('.discovery-item-checkbox:checked');
    console.log('方法1 - 选中的复选框数量:', checkedBoxes.length);

    // 方法2：从全局变量获取
    const globalSelected = window.selectedStreams ? Array.from(window.selectedStreams) : [];
    console.log('方法2 - 全局变量中的选中项:', globalSelected);

    // 优先使用复选框的状态，因为这是最直接的
    let selectedIds = [];
    if (checkedBoxes.length > 0) {
        selectedIds = Array.from(checkedBoxes).map(checkbox => parseInt(checkbox.value));
        console.log('使用复选框状态，选中的ID:', selectedIds);
    } else if (globalSelected.length > 0) {
        selectedIds = globalSelected;
        console.log('使用全局变量状态，选中的ID:', selectedIds);
    }

    if (selectedIds.length === 0) {
        console.log('没有选中任何视频源');
        showGlobalToast('请先选择要测试的视频源', 'warning');
        return;
    }

    console.log(`开始测试 ${selectedIds.length} 个视频源:`, selectedIds);
    showGlobalToast(`开始批量测试 ${selectedIds.length} 个视频源...`, 'info');

    // 使用并发测试，每批最多5个
    await testStreamsInBatchesConcurrent(selectedIds, 5);

    showGlobalToast('批量测试完成', 'success');
    console.log('=== 批量测试任务完成 ===');
}

// 全局的分批并发测试函数
async function testStreamsInBatchesConcurrent(streamIds, batchSize = 5) {
    console.log(`开始分批并发测试，总数: ${streamIds.length}，每批: ${batchSize}`);

    for (let i = 0; i < streamIds.length; i += batchSize) {
        const batch = streamIds.slice(i, i + batchSize);
        console.log(`测试第 ${Math.floor(i / batchSize) + 1} 批，包含视频源:`, batch);

        // 并发测试当前批次的所有视频源
        const testPromises = batch.map(streamId => {
            console.log(`启动测试视频源 ${streamId}`);
            return testSingleStream(streamId);
        });

        try {
            // 等待当前批次的所有测试完成
            await Promise.all(testPromises);
            console.log(`第 ${Math.floor(i / batchSize) + 1} 批测试完成`);

            // 批次间添加短暂延迟，避免过于频繁的请求
            if (i + batchSize < streamIds.length) {
                console.log('批次间等待 1 秒...');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        } catch (error) {
            console.error(`第 ${Math.floor(i / batchSize) + 1} 批测试出现错误:`, error);
            // 即使有错误也继续下一批
        }
    }

    console.log('所有批次并发测试完成');
}

async function continueToManage() {
    console.log('🚀🚀🚀 continueToManage 函数被调用了！🚀🚀🚀');
    console.log('=== continueToManage 开始执行 ===');

    // 确保全局变量存在
    if (!window.selectedStreams) {
        window.selectedStreams = new Set();
        console.log('初始化了空的 window.selectedStreams');
    }

    console.log('当前 window.selectedStreams 状态:', Array.from(window.selectedStreams));

    // 也检查复选框状态作为备用方案
    const checkedBoxes = document.querySelectorAll('.discovery-item-checkbox:checked');
    console.log('找到选中的复选框数量:', checkedBoxes.length);

    if (checkedBoxes.length > 0) {
        console.log('选中的复选框详情:');
        checkedBoxes.forEach((checkbox, index) => {
            console.log(`  复选框 ${index + 1}: value=${checkbox.value}, checked=${checkbox.checked}`);
        });

        if (window.selectedStreams.size === 0) {
            console.log('全局变量为空但有复选框选中，重新同步...');
            // 如果复选框有选中但selectedStreams为空，重新同步
            checkedBoxes.forEach(checkbox => {
                const streamId = parseInt(checkbox.value);
                window.selectedStreams.add(streamId);
                console.log(`同步添加视频源 ${streamId} 到全局变量`);
            });
        }
    }

    const selectedIds = Array.from(window.selectedStreams);
    console.log('最终确定的选中视频源ID列表:', selectedIds);
    console.log('选中数量:', selectedIds.length);

    if (selectedIds.length === 0) {
        console.log('没有选中任何视频源，显示警告');
        showGlobalToast('请先选择一些视频源', 'warning');
        return;
    }

    const { data: { user } } = await globalSupabase.auth.getUser();
    if (!user) {
        showGlobalToast('请先登录', 'warning');
        return;
    }

    // 显示加载状态
    const continueBtn = document.getElementById('continue-to-manage-btn');
    console.log('continueBtn元素:', continueBtn); // 调试日志

    if (!continueBtn) {
        console.error('找不到continue-to-manage-btn元素');
        return;
    }

    const originalText = continueBtn.textContent;
    console.log('原始按钮文字:', originalText); // 调试日志

    continueBtn.disabled = true;
    continueBtn.innerHTML = '<span class="loading-spinner"></span> 正在添加...';
    console.log('设置加载状态后的按钮HTML:', continueBtn.innerHTML); // 调试日志

    let successCount = 0;
    let existingCount = 0;
    let totalCount = selectedIds.length;

    try {
        // 批量检查已存在的视频源，提高效率
        const { data: existingStreams } = await globalSupabase
            .from('user_streams')
            .select('stream_id')
            .eq('user_id', user.id)
            .in('stream_id', selectedIds);

        const existingStreamIds = new Set(existingStreams?.map(s => s.stream_id) || []);
        const newStreamIds = selectedIds.filter(id => !existingStreamIds.has(id));

        existingCount = existingStreamIds.size;
        console.log(`发现 ${existingCount} 个已存在的视频源，${newStreamIds.length} 个新视频源需要添加`);

        if (newStreamIds.length === 0) {
            showGlobalToast('选中的视频源都已在您的播放列表中', 'info');
            // 仍然跳转到个人管理页面，让用户查看现有列表
            setTimeout(() => {
                document.getElementById('discovery-modal').style.display = 'none';
                document.getElementById('personal-modal').style.display = 'flex';
                loadPersonalManagementPage();
            }, 1500);
            return;
        }

        // 获取当前最大排序值
        const { data: maxOrder } = await globalSupabase
            .from('user_streams')
            .select('sort_order')
            .eq('user_id', user.id)
            .order('sort_order', { ascending: false })
            .limit(1)
            .maybeSingle();

        let nextOrder = (maxOrder?.sort_order || 0) + 1;

        // 批量插入新视频源
        const insertData = newStreamIds.map(streamId => ({
            user_id: user.id,
            stream_id: streamId,
            sort_order: nextOrder++
        }));

        const { error } = await globalSupabase
            .from('user_streams')
            .insert(insertData);

        if (!error) {
            successCount = newStreamIds.length;
            // 批量更新使用统计
            for (const streamId of newStreamIds) {
                await updateStreamUsage(streamId);
            }
        } else {
            console.error('批量添加视频源失败:', error);
            throw error;
        }

        // 改进提示信息
        if (existingCount > 0) {
            showGlobalToast(`共处理 ${totalCount} 个视频源，其中新增 ${successCount} 个，已存在 ${existingCount} 个`, 'success');
        } else {
            showGlobalToast(`成功添加 ${successCount} 个视频源到播放列表`, 'success');
        }

        // 清空选择状态，因为已经添加完成
        window.selectedStreams.clear();
        updateSelectionCount();
        updateContinueButton();

        // 延迟一下再跳转，让用户看到成功提示
        setTimeout(() => {
            // 关闭发现页面，打开个人管理页面
            document.getElementById('discovery-modal').style.display = 'none';
            document.getElementById('personal-modal').style.display = 'flex';

            // 加载个人管理页面数据
            loadPersonalManagementPage();
        }, 1000);

    } finally {
        // 恢复按钮状态
        continueBtn.disabled = false;
        continueBtn.textContent = originalText;
    }
}

// 加载个人管理页面数据
async function loadPersonalManagementPage() {
    const { data: { user } } = await globalSupabase.auth.getUser();
    if (!user) {
        console.log('用户未登录');
        return;
    }

    const personalChannels = document.getElementById('personal-channels');
    const emptyPersonal = document.getElementById('empty-personal');

    if (!personalChannels) {
        console.error('找不到personal-channels元素');
        return;
    }

    console.log('开始加载个人管理页面数据...');

    // 显示加载动画
    personalChannels.innerHTML = `
        <div class="page-loading">
            <div class="loading-spinner-large"></div>
            <div class="loading-text">正在加载您的视频源列表...</div>
        </div>
    `;

    try {
        // 使用JOIN查询获取完整信息，提高效率和数据一致性
        const { data: userStreams, error } = await globalSupabase
            .from('user_streams')
            .select(`
                id, custom_name, sort_order, is_favorite,
                streams!inner (
                    id, name, url, channel, quality, status, usage_count
                )
            `)
            .eq('user_id', user.id)
            .eq('streams.status', 'active')
            .order('sort_order', { ascending: true });

        if (error) {
            console.error('加载个人列表失败:', error);
            personalChannels.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">⚠️</div>
                    <h3>加载失败</h3>
                    <p>无法加载您的视频源列表，请稍后重试</p>
                    <button onclick="loadPersonalManagementPage()" class="retry-btn">重试</button>
                </div>
            `;
            return;
        }

        console.log('获取到用户视频源:', userStreams);

        if (!userStreams || userStreams.length === 0) {
            console.log('用户没有视频源，显示空状态');
            personalChannels.style.display = 'none';
            emptyPersonal.style.display = 'block';
            return;
        }

        // 数据已经合并，直接处理显示名称
        const combinedData = userStreams.map(userStream => ({
            ...userStream,
            displayName: userStream.custom_name || userStream.streams.name
        }));

        console.log('合并后的数据:', combinedData);

        personalChannels.style.display = 'block';
        emptyPersonal.style.display = 'none';

        // 按频道分组
        const channelGroups = groupStreamsByChannel(combinedData);
        console.log('频道分组:', channelGroups);
        displayPersonalChannelGroups(channelGroups);

        // 更新统计信息
        updatePersonalStats(combinedData);

    } catch (error) {
        console.error('加载个人管理页面异常:', error);
    }
}

function groupStreamsByChannel(userStreams) {
    const groups = {};
    userStreams.forEach(userStream => {
        const channel = (userStream.streams && userStream.streams.channel) || '其他频道';
        if (!groups[channel]) {
            groups[channel] = [];
        }
        groups[channel].push(userStream);
    });
    console.log('分组结果:', groups);
    return groups;
}

function displayPersonalChannelGroups(channelGroups) {
    const personalChannels = document.getElementById('personal-channels');
    if (!personalChannels) {
        console.error('找不到personal-channels元素');
        return;
    }

    const channelNames = Object.keys(channelGroups);
    console.log('要显示的频道:', channelNames);

    if (channelNames.length === 0) {
        console.log('没有频道数据');
        personalChannels.innerHTML = '<div class="empty">没有视频源数据</div>';
        return;
    }

    personalChannels.innerHTML = channelNames.map(channelName => {
        const streams = channelGroups[channelName];
        const channelIcon = getChannelIcon(channelName);

        console.log(`渲染频道 ${channelName}:`, streams);

        return `
            <div class="channel-group" data-channel="${channelName}">
                <div class="channel-header" onclick="toggleChannelGroup('${channelName}')">
                    <div class="channel-title">
                        <span class="channel-icon">${channelIcon}</span>
                        <span>${channelName}</span>
                    </div>
                    <div class="channel-info">
                        <span class="channel-count">${streams.length}</span>
                        <span class="channel-toggle">▼</span>
                    </div>
                </div>
                <div class="channel-streams" id="streams-${channelName.replace(/\s+/g, '-')}">
                    ${streams.map(stream => {
                        const streamData = stream.streams || {};
                        const displayName = stream.displayName || streamData.name || '未知视频源';
                        const streamUrl = streamData.url || '';
                        const streamQuality = streamData.quality || '';
                        const streamId = streamData.id || stream.stream_id;

                        console.log(`渲染视频源 ${displayName}, streamId: ${streamId}, streamData:`, streamData);

                        return `
                            <div class="personal-stream-item" data-user-stream-id="${stream.id}" draggable="true">
                                <div class="stream-drag-handle">⋮⋮</div>
                                <div class="personal-stream-info">
                                    <div class="personal-stream-name">
                                        <input type="text" value="${displayName}"
                                               onchange="updateCustomName(${stream.id}, this.value)">
                                    </div>
                                    <div class="personal-stream-url">${streamUrl}</div>
                                    <div class="personal-stream-meta">
                                        ${streamQuality ? `<span class="tag quality">${streamQuality}</span>` : ''}
                                        <span class="tag usage">使用 ${streamData.usage_count || 0} 次</span>
                                    </div>
                                </div>
                                <div class="personal-stream-status">
                                    <div class="test-result" id="test-result-${streamId}">未测试</div>
                                </div>
                                <div class="personal-stream-actions">
                                    <button onclick="testSingleStream(${streamId})" class="secondary-btn">测试</button>
                                    <button onclick="previewStreamInWindow(${streamId}, '${displayName.replace(/'/g, "\\'")}', '${streamUrl.replace(/'/g, "\\'")}')" class="secondary-btn">预览</button>
                                    <button onclick="removeFromPersonalList(${stream.id})" class="remove-btn">删除</button>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }).join('');

    console.log('频道组HTML已生成');

    // 为每个频道启用拖拽排序
    channelNames.forEach(channelName => {
        enableChannelDragSort(channelName);
    });
}

function getChannelIcon(channelName) {
    const iconMap = {
        'CCTV5': '📺',
        '五星体育': '⭐',
        '广东体育': '🏆',
        'Sky Sports': '🌟',
        'ESPN': '🏈',
        'Fox Sports': '🦊',
        '其他频道': '📡'
    };
    return iconMap[channelName] || '📺';
}

function updatePersonalStats(userStreams) {
    const totalStreams = document.getElementById('total-streams');

    if (totalStreams) totalStreams.textContent = userStreams.length;
}

// 从DOM更新统计信息
function updatePersonalStatsFromDOM() {
    const totalStreams = document.getElementById('total-streams');
    const streamItems = document.querySelectorAll('.personal-stream-item');

    if (totalStreams) totalStreams.textContent = streamItems.length;
}

async function backToDiscovery() {
    console.log('=== 返回发现页面 ===');
    console.log('返回前的全局选择状态:', window.selectedStreams ? Array.from(window.selectedStreams) : []);

    // 检查是否有本地删除操作需要同步
    const hasLocalDeletes = window.deletedStreamIds && window.deletedStreamIds.size > 0;
    if (hasLocalDeletes) {
        console.log('检测到本地删除操作，将同步到数据库');
        showGlobalToast('正在同步删除操作...', 'info');
    }

    // 先同步本地删除操作到数据库
    await syncLocalDeletesToDatabase();

    // 然后更新UI显示状态
    await updateUIStateFromDatabase();

    if (hasLocalDeletes) {
        showGlobalToast('删除操作已同步', 'success');
    }

    document.getElementById('personal-modal').style.display = 'none';
    document.getElementById('discovery-modal').style.display = 'flex';

    // 恢复发现页面的选中状态
    restoreDiscoverySelections();

    console.log('=== 返回发现页面完成 ===');
}

// 同步本地删除操作到数据库
async function syncLocalDeletesToDatabase() {
    if (!window.deletedStreamIds || window.deletedStreamIds.size === 0) {
        console.log('没有本地删除操作需要同步');
        return;
    }

    console.log('开始同步本地删除操作到数据库...');
    const deletedIds = Array.from(window.deletedStreamIds);
    console.log('要删除的用户视频源ID:', deletedIds);

    try {
        for (const userStreamId of deletedIds) {
            console.log(`删除数据库中的用户视频源 ${userStreamId}`);
            const { error } = await globalSupabase
                .from('user_streams')
                .delete()
                .eq('id', userStreamId);

            if (error) {
                console.error(`删除用户视频源 ${userStreamId} 失败:`, error);
            } else {
                console.log(`成功删除用户视频源 ${userStreamId}`);
            }
        }

        // 清空本地删除记录
        window.deletedStreamIds.clear();
        console.log('本地删除记录已清空');

    } catch (error) {
        console.error('同步删除操作异常:', error);
    }
}

// 从数据库更新UI显示状态（不影响选择状态）
async function updateUIStateFromDatabase() {
    console.log('从数据库更新UI显示状态...');

    const { data: { user } } = await globalSupabase.auth.getUser();
    if (!user) {
        console.log('用户未登录，无法更新UI状态');
        return;
    }

    try {
        // 获取用户当前在数据库中的所有视频源
        const { data: userStreams, error } = await globalSupabase
            .from('user_streams')
            .select('stream_id')
            .eq('user_id', user.id);

        if (error) {
            console.error('获取用户视频源失败:', error);
            return;
        }

        const databaseStreamIds = userStreams.map(us => us.stream_id);
        console.log('数据库中的视频源ID:', databaseStreamIds);

        // 只更新UI显示状态，不修改选择状态
        databaseStreamIds.forEach(streamId => {
            const item = document.querySelector(`[data-stream-id="${streamId}"]`);
            if (item) {
                item.classList.add('added');
            }
        });

        console.log('UI状态更新完成');

    } catch (error) {
        console.error('更新UI状态异常:', error);
    }
}

// 恢复发现页面的选中状态
function restoreDiscoverySelections() {
    // 确保全局变量存在
    if (!window.selectedStreams) {
        window.selectedStreams = new Set();
    }

    console.log('恢复选中状态:', Array.from(window.selectedStreams));

    // 根据selectedStreams恢复复选框状态和视觉状态
    const checkboxes = document.querySelectorAll('.discovery-item-checkbox');
    let restoredCount = 0;

    checkboxes.forEach(checkbox => {
        const streamId = parseInt(checkbox.value);
        const shouldBeChecked = window.selectedStreams.has(streamId);

        // 恢复复选框状态
        checkbox.checked = shouldBeChecked;

        // 恢复视觉状态
        const item = document.querySelector(`[data-stream-id="${streamId}"]`);
        if (item) {
            item.classList.toggle('selected', shouldBeChecked);
        }

        if (shouldBeChecked) {
            restoredCount++;
            console.log(`恢复选中状态: 视频源 ${streamId}`);
        }
    });

    console.log(`总共恢复了 ${restoredCount} 个视频源的选中状态`);

    // 更新选择计数和按钮状态
    updateSelectionCount();
    updateContinueButton();

    // 更新全选按钮状态
    updateSelectAllButtonState();
}

// 更新全选按钮状态
function updateSelectAllButtonState() {
    const selectAllBtn = document.getElementById('select-all-btn');
    if (!selectAllBtn) return;

    const checkboxes = document.querySelectorAll('.discovery-item-checkbox');
    if (checkboxes.length === 0) return;

    const allSelected = Array.from(checkboxes).every(cb => cb.checked);
    const noneSelected = Array.from(checkboxes).every(cb => !cb.checked);

    if (allSelected) {
        selectAllBtn.textContent = '取消全选';
    } else {
        selectAllBtn.textContent = '全选';
    }

    console.log('更新全选按钮状态:', selectAllBtn.textContent);
}

// 频道组操作函数
function toggleChannelGroup(channelName) {
    const channelGroup = document.querySelector(`[data-channel="${channelName}"]`);
    if (channelGroup) {
        channelGroup.classList.toggle('collapsed');
    }
}

function enableChannelDragSort(channelName) {
    const channelId = channelName.replace(/\s+/g, '-');
    const streamsContainer = document.getElementById(`streams-${channelId}`);

    if (!streamsContainer || typeof Sortable === 'undefined') return;

    new Sortable(streamsContainer, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        handle: '.stream-drag-handle',
        onStart: function(evt) {
            evt.item.classList.add('dragging');
        },
        onEnd: async function(evt) {
            evt.item.classList.remove('dragging');

            // 更新排序
            const items = Array.from(streamsContainer.children);
            const updates = items.map((item, index) => ({
                id: parseInt(item.dataset.userStreamId),
                sort_order: index
            }));

            await updatePersonalStreamOrder(updates);
        }
    });
}

async function updatePersonalStreamOrder(updates) {
    try {
        for (const update of updates) {
            const { error } = await globalSupabase
                .from('user_streams')
                .update({ sort_order: update.sort_order })
                .eq('id', update.id);

            if (error) {
                console.error('更新排序失败:', error);
            }
        }
    } catch (error) {
        console.error('更新排序异常:', error);
    }
}

// 个人列表操作函数已简化，移除收藏功能

// 保存功能
async function saveAndPlay() {
    const { data: { user } } = await globalSupabase.auth.getUser();
    if (!user) {
        showGlobalToast('请先登录', 'warning');
        return;
    }

    try {
        // 显示保存中状态
        const saveBtn = document.getElementById('save-and-play-btn');
        const originalText = saveBtn.textContent;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<span class="loading-spinner"></span> 保存中...';

        // 处理删除的项目
        if (window.deletedStreamIds && window.deletedStreamIds.size > 0) {
            const deletedIds = Array.from(window.deletedStreamIds);
            console.log('删除以下视频源:', deletedIds);

            for (const userStreamId of deletedIds) {
                try {
                    await globalSupabase
                        .from('user_streams')
                        .delete()
                        .eq('id', userStreamId);
                } catch (error) {
                    console.error(`删除视频源 ${userStreamId} 失败:`, error);
                }
            }

            // 清空删除记录
            window.deletedStreamIds.clear();
        }

        // 处理排序更新
        const streamItems = document.querySelectorAll('.personal-stream-item');
        const updates = Array.from(streamItems).map((item, index) => ({
            id: parseInt(item.dataset.userStreamId),
            sort_order: index
        }));

        if (updates.length > 0) {
            console.log('更新排序:', updates);
            for (const update of updates) {
                try {
                    await globalSupabase
                        .from('user_streams')
                        .update({ sort_order: update.sort_order })
                        .eq('id', update.id);
                } catch (error) {
                    console.error(`更新排序 ${update.id} 失败:`, error);
                }
            }
        }

        showGlobalToast('保存成功', 'success');

        // 关闭个人管理页面
        setTimeout(() => {
            document.getElementById('personal-modal').style.display = 'none';
        }, 1000);

    } catch (error) {
        console.error('保存异常:', error);
        showGlobalToast('保存失败', 'error');
    } finally {
        // 恢复按钮状态
        const saveBtn = document.getElementById('save-and-play-btn');
        saveBtn.disabled = false;
        saveBtn.textContent = '保存';
    }
}

// 预览窗口功能
let previewHls = null;

function previewStreamInWindow(streamId, streamName, streamUrl) {
    const previewWindow = document.getElementById('preview-window');
    const previewTitle = document.getElementById('preview-title');
    const previewVideo = document.getElementById('preview-video');

    if (!previewWindow || !previewVideo) {
        showGlobalToast('预览窗口未找到', 'error');
        return;
    }

    // 设置标题
    previewTitle.textContent = `预览: ${streamName}`;

    // 显示预览窗口
    previewWindow.style.display = 'block';

    // 加载视频流
    loadPreviewStream(streamUrl, previewVideo);

    // 绑定关闭按钮事件（如果还没绑定）
    const closeBtn = document.getElementById('preview-close');
    if (closeBtn && !closeBtn.hasAttribute('data-bound')) {
        closeBtn.addEventListener('click', closePreviewWindow);
        closeBtn.setAttribute('data-bound', 'true');
    }

    // 使预览窗口可拖拽
    makePreviewDraggable();
}

function loadPreviewStream(sourceUrl, videoElement) {
    if (!sourceUrl || !videoElement) return;

    // 清理之前的HLS实例
    if (previewHls) {
        previewHls.destroy();
        previewHls = null;
    }

    if (Hls.isSupported()) {
        previewHls = new Hls();
        previewHls.loadSource(sourceUrl);
        previewHls.attachMedia(videoElement);

        previewHls.on(Hls.Events.MANIFEST_PARSED, () => {
            videoElement.play().catch(e => {
                console.log("预览自动播放被阻止，用户需要手动播放");
            });
        });

        previewHls.on(Hls.Events.ERROR, (event, data) => {
            if (data.fatal) {
                console.error('预览加载失败:', data);
                showGlobalToast('预览加载失败', 'error');
            }
        });
    } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
        videoElement.src = sourceUrl;
        videoElement.addEventListener('loadedmetadata', () => {
            videoElement.play().catch(e => {
                console.log("预览自动播放被阻止，用户需要手动播放");
            });
        });
    } else {
        showGlobalToast('浏览器不支持此视频格式', 'error');
    }
}

function closePreviewWindow() {
    const previewWindow = document.getElementById('preview-window');
    const previewVideo = document.getElementById('preview-video');

    if (previewWindow) {
        previewWindow.style.display = 'none';
    }

    // 停止视频播放
    if (previewVideo) {
        previewVideo.pause();
        previewVideo.src = '';
    }

    // 清理HLS实例
    if (previewHls) {
        previewHls.destroy();
        previewHls = null;
    }
}

function makePreviewDraggable() {
    const previewWindow = document.getElementById('preview-window');
    const previewHeader = document.querySelector('.preview-header');

    if (!previewWindow || !previewHeader) return;

    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;
    let xOffset = 0;
    let yOffset = 0;

    previewHeader.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', dragMove);
    document.addEventListener('mouseup', dragEnd);

    function dragStart(e) {
        initialX = e.clientX - xOffset;
        initialY = e.clientY - yOffset;

        if (e.target === previewHeader || previewHeader.contains(e.target)) {
            isDragging = true;
        }
    }

    function dragMove(e) {
        if (isDragging) {
            e.preventDefault();
            currentX = e.clientX - initialX;
            currentY = e.clientY - initialY;

            xOffset = currentX;
            yOffset = currentY;

            previewWindow.style.transform = `translate(${currentX}px, ${currentY}px)`;
        }
    }

    function dragEnd() {
        isDragging = false;
    }
}