# Cloudflare Turnstile 设置指南

## 1. 获取 Turnstile 密钥

### 步骤1：登录 Cloudflare Dashboard
1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 使用你的 Cloudflare 账户登录

### 步骤2：创建 Turnstile 站点
1. 在左侧菜单中找到 **Turnstile**
2. 点击 **Add Site**
3. 填写站点信息：
   - **Site name**: F1 Live Stream
   - **Domain**: 你的域名（如 `localhost` 用于本地开发）
   - **Widget Mode**: Managed (推荐)
4. 点击 **Create**

### 步骤3：获取密钥
创建成功后，你会看到：
- **Site Key** (公开密钥)：用于前端
- **Secret Key** (私密密钥)：用于后端验证

## 2. 配置密钥

### 本地开发
1. 复制 `.env.example` 为 `.env`
2. 将 `TURNSTILE_SECRET_KEY` 替换为你的真实 Secret Key

### 生产环境 (Supabase)
1. 登录 Supabase Dashboard
2. 进入你的项目
3. 点击 **Edge Functions**
4. 点击 **Manage secrets** 或 **Edge Function Secrets**
5. 添加密钥：
   - **Name**: `TURNSTILE_SECRET_KEY`
   - **Value**: 你的 Secret Key

### 更新前端 Site Key
如果你的 Site Key 不是 `0x4AAAAAABof2Gg619dti5bh`，需要更新：
1. 打开 `index.html`
2. 找到 `data-sitekey="0x4AAAAAABof2Gg619dti5bh"`
3. 替换为你的 Site Key

## 3. 部署更新

```bash
# 重新部署 Edge Function
npx supabase functions deploy sign-up-with-invite
```

## 4. 测试验证

1. 打开注册页面
2. 填写邮箱和密码
3. 完成 Turnstile 验证（勾选框或滑块）
4. 提交注册

如果配置正确，应该能看到 Turnstile 验证组件，并且只有完成验证后才能成功注册。

## 5. 故障排除

### 常见问题：
1. **Turnstile 不显示**：检查 Site Key 是否正确
2. **验证失败**：检查 Secret Key 是否正确配置
3. **域名错误**：确保 Turnstile 站点配置的域名与实际域名匹配

### 调试方法：
1. 打开浏览器开发者工具
2. 查看 Console 是否有错误信息
3. 检查 Network 标签页的请求响应
