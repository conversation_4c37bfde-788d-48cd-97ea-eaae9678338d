/* --- 全局与根变量 --- */
:root {
    --primary-bg: #121212;
    --secondary-bg: #1e1e1e;
    --tertiary-bg: #2a2a2a;
    --primary-text: #e0e0e0;
    --secondary-text: #a0a0a0;
    --accent-color: #e10600; /* F1 Red */
    --accent-hover: #ff3333;
    --border-color: #333;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
}

body {
    background-color: var(--primary-bg);
    color: var(--primary-text);
    font-family: var(--font-family);
    margin: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

a {
    color: var(--accent-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* --- Header --- */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
}

header .logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-color);
}

header nav a {
    margin: 0 1rem;
    color: var(--primary-text);
    font-weight: 500;
}

header nav a:hover {
    color: var(--accent-hover);
    text-decoration: none;
}

/* --- User Authentication Area --- */
.user-auth {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

/* --- User Avatar and Dropdown --- */
.user-avatar-container {
    position: relative;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.user-avatar:hover {
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.avatar-text {
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-transform: uppercase;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
}

.dropdown-header span {
    color: var(--primary-text);
    font-size: 0.9rem;
    font-weight: 500;
    word-break: break-all;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 4px 0;
}

.dropdown-item {
    width: 100%;
    padding: 12px 16px;
    background: none;
    border: none;
    color: var(--primary-text);
    font-size: 0.9rem;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dropdown-item:hover {
    background-color: var(--tertiary-bg);
}

.dropdown-icon {
    font-size: 16px;
}

#login-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
    white-space: nowrap;
}

#login-btn:hover {
    background-color: var(--accent-hover);
}

/* --- Main Content --- */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    width: 100%;
    max-width: 1400px; /* 增加最大宽度以容纳聊天室 */
    margin: 0 auto;
    box-sizing: border-box;
    gap: 1rem;
}

/* --- 视频和聊天室容器 --- */
.video-chat-container {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.video-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.video-container {
    position: relative;
    width: 100%;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
}

#video-player {
    width: 100%;
    height: auto;
    display: block;
}

.danmaku-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

/* --- 聊天室样式 --- */
.chat-section {
    width: 320px;
    background-color: var(--secondary-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.chat-section.hidden {
    display: none;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--tertiary-bg);
    border-bottom: 1px solid var(--border-color);
    border-radius: 8px 8px 0 0;
}

.chat-title {
    font-weight: 500;
    color: var(--primary-text);
}

.chat-close-btn {
    background: none;
    border: none;
    color: var(--primary-text);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    font-size: 1.2rem;
    line-height: 1;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-close-btn:hover {
    background-color: var(--border-color);
}

.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 400px;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background-color: var(--primary-bg);
}

.chat-input-container {
    display: flex;
    padding: 0.75rem;
    gap: 0.5rem;
    border-top: 1px solid var(--border-color);
}

.chat-input-container input {
    flex: 1;
    padding: 0.5rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--primary-text);
    border-radius: 4px;
    font-size: 0.9rem;
}

.chat-input-container button {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* --- 聊天消息样式 --- */
.chat-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: var(--secondary-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.chat-message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.chat-user {
    font-weight: 500;
    color: var(--accent-color);
    font-size: 0.9rem;
}

.chat-time {
    font-size: 0.8rem;
    color: var(--secondary-text);
}

.chat-message-content {
    color: var(--primary-text);
    line-height: 1.4;
    font-size: 0.9rem;
}

/* --- 聊天室浮层 --- */
.chat-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
}

.chat-float:hover {
    background-color: var(--accent-hover);
    transform: scale(1.1);
}

.chat-float-icon {
    color: white;
    font-size: 1.5rem;
}

.chat-float.show {
    display: flex;
}

/* --- 弹幕发送区域（紧跟视频下方） --- */
.danmaku-sender {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: var(--secondary-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* --- 视频源选择区域 --- */
.source-container {
    background-color: var(--secondary-bg);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.manual-input-section h3,
.channel-sources-section h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-text);
    font-size: 1.1rem;
    font-weight: 500;
}

.stream-selector {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* --- 视频源选择tab --- */
.source-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.source-tab {
    padding: 0.75rem 1.5rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px 6px 0 0;
    color: var(--primary-text);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    font-weight: 500;
}

.source-tab:hover {
    background-color: var(--border-color);
}

.source-tab.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
    border-bottom-color: var(--accent-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* --- 频道和视频源列表 --- */
.channels-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.channel-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.channel-tab {
    padding: 0.5rem 1rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--primary-text);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.channel-tab:hover {
    background-color: var(--border-color);
}

.channel-tab.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.sources-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.source-item {
    padding: 1rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.source-item:hover {
    border-color: var(--accent-color);
    background-color: var(--border-color);
}

.source-item.active {
    border-color: var(--accent-color);
    background-color: rgba(225, 6, 0, 0.1);
}

.source-name {
    font-weight: 500;
    color: var(--primary-text);
    margin-bottom: 0.5rem;
}

.source-quality {
    font-size: 0.8rem;
    color: var(--secondary-text);
    background-color: var(--primary-bg);
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    display: inline-block;
}

input[type="text"], select {
    flex: 1;
    padding: 0.75rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--primary-text);
    border-radius: 4px;
    font-size: 1rem;
}

input[type="text"]::placeholder {
    color: var(--secondary-text);
}

button {
    padding: 0.75rem 1.5rem;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: background-color 0.2s ease;
}

button:hover {
    background-color: var(--accent-hover);
}

/* --- Footer --- */
footer {
    text-align: center;
    padding: 1rem;
    background-color: var(--secondary-bg);
    border-top: 1px solid var(--border-color);
    color: var(--secondary-text);
    font-size: 0.9rem;
    width: 100%;
    box-sizing: border-box;
}

/* --- Danmaku Styles --- */
.danmaku {
    position: absolute;
    right: 0;
    transform: translateX(100%);
    white-space: nowrap;
    padding: 5px 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 1.2rem;
    border-radius: 4px;
    animation: scroll-left 10s linear forwards;
    pointer-events: none; /* So they don't interfere with video controls */
}

@keyframes scroll-left {
    from {
        right: 0;
        transform: translateX(100%);
    }
    to {
        right: 100%;
        transform: translateX(0%);
    }
}

/* --- Modal Styles --- */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--secondary-bg);
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

/* 旧的通用关闭按钮样式已被 .auth-close-btn 替代 */

.modal-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.tab-link {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    color: var(--secondary-text);
    font-size: 1rem;
}

.tab-link.active {
    color: var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* --- Table Styles for Modal --- */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

thead {
    background-color: var(--tertiary-bg);
}

th, td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

tr:last-child td {
    border-bottom: none;
}

/* --- Auth Modal Styles --- */
.auth-modal-content {
    max-width: 420px;
    text-align: center;
    position: relative; /* 确保关闭按钮相对于此容器定位 */
}

.auth-modal-content h2 {
    color: var(--primary-text);
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: var(--secondary-text);
    margin-top: 0;
    margin-bottom: 1.5rem;
}

.auth-modal-content form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.auth-modal-content input {
    width: 100%;
    padding: 0.8rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--primary-text);
    border-radius: 4px;
    font-size: 1rem;
    box-sizing: border-box;
}

.auth-modal-content button[type="submit"],
.auth-modal-content .dropdown-item {
    width: 100%;
    padding: 0.8rem;
}

/* 关闭按钮不应该占满整行 */
.auth-modal-content .auth-close-btn {
    width: 32px;
    height: 32px;
    padding: 0;
}

.auth-message {
    color: var(--accent-hover);
    min-height: 20px;
    text-align: left;
    font-size: 0.9rem;
}

.auth-switch {
    margin-top: 1.5rem;
    color: var(--secondary-text);
}

.auth-switch a {
    font-weight: bold;
    cursor: pointer;
}

/* 统一的关闭按钮样式 */
.auth-close-btn {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 32px;
    height: 32px;
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
    border: none;
    border-radius: 6px;
    font-size: 1.2rem;
    line-height: 32px; /* Vertically center the '×' */
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    z-index: 10;
    padding: 0;
}

.auth-close-btn:hover {
    background-color: #4a4a4a; /* Darker background on hover */
}

/* 保持向后兼容 */
#auth-close-btn {
    /* 继承 .auth-close-btn 的样式 */
}

/* --- Toast Notifications --- */
.toast {
    position: fixed;
    top: 80px; /* 下移，避免与 header 重叠 */
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    font-size: 14px;
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    max-width: 300px;
    word-wrap: break-word;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

/* 响应式设计：移动设备 */
@media (max-width: 768px) {
    /* Header 响应式 */
    header {
        padding: 0.75rem 1rem;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    header .logo {
        font-size: 1.2rem;
    }

    header nav {
        order: 3;
        width: 100%;
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 0.5rem;
    }

    header nav a {
        margin: 0;
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
        background-color: var(--tertiary-bg);
        border-radius: 4px;
        border: 1px solid var(--border-color);
    }

    /* 用户信息区域 */
    .user-auth {
        gap: 0.5rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
    }

    .avatar-text {
        font-size: 12px;
    }

    .user-dropdown {
        min-width: 180px;
        right: -10px;
    }

    .dropdown-header span {
        font-size: 0.8rem;
    }

    .dropdown-item {
        padding: 10px 12px;
        font-size: 0.8rem;
    }

    .dropdown-icon {
        font-size: 14px;
    }

    #login-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    /* 视频和聊天室响应式 */
    .video-chat-container {
        flex-direction: column;
    }

    .chat-section {
        width: 100%;
        order: 2;
    }

    .chat-content {
        height: 200px;
    }

    /* 视频源列表响应式 */
    .sources-list {
        grid-template-columns: 1fr;
    }

    .channel-tabs {
        justify-content: center;
    }

    .source-tabs {
        justify-content: center;
    }

    .source-tab {
        flex: 1;
        text-align: center;
    }

    .stream-selector {
        flex-direction: column;
        align-items: stretch;
    }

    .danmaku-sender {
        flex-direction: column;
        align-items: stretch;
    }

    /* Toast 响应式 */
    .toast {
        top: 70px;
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .toast.show {
        transform: translateY(0);
    }

    /* 移动端浮层调整 */
    .chat-float {
        bottom: 80px;
        right: 15px;
        width: 50px;
        height: 50px;
    }

    .chat-float-icon {
        font-size: 1.2rem;
    }

    /* 大仓库和管理员界面响应式 */
    .large-modal {
        max-width: 95%;
        max-height: 90vh;
    }

    .warehouse-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-section, .filter-section, .batch-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .search-section input {
        min-width: auto;
        width: 100%;
    }

    .admin-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .admin-tabs {
        flex-wrap: wrap;
    }

    .admin-tab {
        flex: 1;
        text-align: center;
        min-width: 120px;
    }

    .stream-item, .admin-stream-item {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .stream-actions, .admin-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .form-row {
        flex-direction: column;
    }

    .report-stats {
        flex-direction: column;
    }

    .stat-card {
        text-align: center;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    header {
        padding: 0.5rem 0.75rem;
    }

    header .logo {
        font-size: 1rem;
    }

    header nav a {
        margin: 0 0.25rem;
        font-size: 0.8rem;
    }

    .user-avatar {
        width: 28px;
        height: 28px;
    }

    .avatar-text {
        font-size: 11px;
    }

    #login-btn {
        padding: 0.35rem 0.6rem;
        font-size: 0.75rem;
    }

    /* 重置密码确认对话框移动端适配 */
    .reset-confirmation-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }

    .secondary-btn, .primary-btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .forgot-password-link {
        text-align: center;
    }

    .forgot-password-link a {
        font-size: 0.8rem;
    }
}

.toast-success {
    background-color: var(--success-color);
}

.toast-error {
    background-color: var(--error-color);
}

.toast-warning {
    background-color: var(--warning-color);
}

.toast-info {
    background-color: var(--accent-color);
}

/* --- Auth Message Styles --- */
.auth-message {
    margin: 10px 0;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.auth-message.error {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.auth-message.warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.auth-message.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

/* --- Forgot Password Link --- */
.forgot-password-link {
    text-align: right;
    margin: -0.5rem 0 1rem 0;
}

.forgot-password-link a {
    color: var(--accent-color);
    font-size: 0.85rem;
    text-decoration: none;
    transition: color 0.2s ease;
}

.forgot-password-link a:hover {
    color: var(--accent-hover);
    text-decoration: underline;
}

/* --- Reset Password Confirmation Modal --- */
.reset-confirmation-content {
    text-align: center;
    padding: 1rem 0;
}

.reset-confirmation-content p {
    margin: 0.5rem 0;
    color: var(--primary-text);
}

.user-email-display {
    font-weight: bold;
    color: var(--accent-color);
    background-color: var(--tertiary-bg);
    padding: 0.5rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.reset-warning {
    font-size: 0.9rem;
    color: var(--secondary-text);
    font-style: italic;
}

.reset-confirmation-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    justify-content: center;
}

.secondary-btn, .primary-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.secondary-btn {
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
    border: 1px solid var(--border-color);
}

.secondary-btn:hover {
    background-color: var(--border-color);
}

.primary-btn {
    background-color: var(--accent-color);
    color: white;
}

.primary-btn:hover {
    background-color: var(--accent-hover);
}

/* --- Admin Configuration Modal --- */
.admin-config-content {
    padding: 1rem 0;
}

.config-item {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.config-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    color: var(--primary-text);
    cursor: pointer;
    font-size: 1rem;
}

.config-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-color);
    cursor: pointer;
}

.config-description {
    margin: 0.75rem 0 0 2.25rem;
    font-size: 0.85rem;
    color: var(--secondary-text);
    line-height: 1.4;
}

#save-config-btn {
    width: 100%;
    margin-top: 1rem;
}

/* --- Turnstile Verification --- */
.turnstile-container {
    margin: 1rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.cf-turnstile {
    margin: 0 auto;
}

/* 暗色主题下的 Turnstile 样式调整 */
.turnstile-container iframe {
    border-radius: 4px;
}

/* --- 筛选模态框样式 --- */
.filter-content {
    padding: 1rem 0;
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.filter-options label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1rem;
    color: var(--primary-text);
    cursor: pointer;
    padding: 0.75rem;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.filter-options label:hover {
    background-color: var(--border-color);
}

.filter-options input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-color);
    cursor: pointer;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.filter-actions button {
    min-width: 120px;
}

/* --- 大仓库模态框样式 --- */
.large-modal {
    max-width: 1200px;
    max-height: 85vh;
}

.warehouse-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    align-items: center;
    padding: 1rem;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.search-section, .filter-section, .batch-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-section input {
    min-width: 200px;
}

/* --- 视频源列表样式 --- */
.stream-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--primary-bg);
}

.stream-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.stream-item:hover {
    background-color: var(--tertiary-bg);
}

.stream-item:last-child {
    border-bottom: none;
}

.stream-checkbox {
    flex-shrink: 0;
}

.stream-info {
    flex: 1;
    min-width: 0;
}

.stream-name {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 500;
    color: var(--primary-text);
}

.stream-url {
    margin: 0 0 0.5rem 0;
    font-size: 0.85rem;
    color: var(--secondary-text);
    word-break: break-all;
}

.stream-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.stream-meta {
    font-size: 0.8rem;
    color: var(--secondary-text);
}

.stream-status {
    flex-shrink: 0;
    min-width: 100px;
    text-align: center;
}

.test-result {
    display: block;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
}

.test-result.testing {
    color: var(--warning-color);
    background-color: rgba(255, 152, 0, 0.1);
}

.test-result.success {
    color: var(--success-color);
    background-color: rgba(76, 175, 80, 0.1);
}

.test-result.failed {
    color: var(--error-color);
    background-color: rgba(244, 67, 54, 0.1);
}

.test-progress {
    margin-top: 0.5rem;
    height: 2px;
    background-color: var(--border-color);
    border-radius: 1px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--accent-color);
    animation: progress-animation 1.5s infinite;
}

@keyframes progress-animation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.stream-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.stream-actions button {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 4px;
    white-space: nowrap;
}

/* --- 分页样式 --- */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
}

.pagination button {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* --- 表单样式 --- */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-text);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--primary-text);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
}

.form-row {
    display: flex;
    gap: 1rem;
}

.form-row .form-group {
    flex: 1;
}

.form-hint {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--secondary-text);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.form-actions button {
    min-width: 120px;
}

.test-result-panel {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.test-progress {
    color: var(--warning-color);
    font-weight: 500;
}

.test-success {
    color: var(--success-color);
    font-weight: 500;
}

.test-error {
    color: var(--error-color);
    font-weight: 500;
}

/* --- 举报模态框样式 --- */
.report-content {
    text-align: left;
}

.report-stream-info {
    background-color: var(--tertiary-bg);
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.report-stream-info h4 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-text);
}

.report-stream-info p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--secondary-text);
    word-break: break-all;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    border: 1px solid var(--border-color);
}

.radio-label:hover {
    background-color: var(--tertiary-bg);
}

.radio-label input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-color);
    cursor: pointer;
}

.radio-label span {
    color: var(--primary-text);
    font-size: 0.95rem;
}

/* --- 管理员界面样式 --- */
.admin-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

.admin-tab {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    color: var(--secondary-text);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 0.95rem;
    font-weight: 500;
}

.admin-tab:hover {
    color: var(--primary-text);
    background-color: var(--tertiary-bg);
}

.admin-tab.active {
    color: var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
    background-color: rgba(225, 6, 0, 0.05);
}

.admin-tab-content {
    display: none;
}

.admin-tab-content.active {
    display: block;
}

.admin-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.filter-controls, .batch-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.admin-list {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--primary-bg);
}

.admin-stream-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.admin-stream-item:hover {
    background-color: var(--tertiary-bg);
}

.admin-stream-item:last-child {
    border-bottom: none;
}

.admin-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.admin-actions button {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 4px;
    font-weight: 500;
    white-space: nowrap;
}

.approve-btn {
    background-color: var(--success-color);
    color: white;
    border: none;
}

.approve-btn:hover {
    background-color: #45a049;
}

.reject-btn, .suspend-btn {
    background-color: var(--warning-color);
    color: white;
    border: none;
}

.reject-btn:hover, .suspend-btn:hover {
    background-color: #e68900;
}

.delete-btn {
    background-color: var(--error-color);
    color: white;
    border: none;
}

.delete-btn:hover {
    background-color: #d32f2f;
}

.restore-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
}

.restore-btn:hover {
    background-color: #cc0500;
}

.reports-btn {
    background-color: var(--secondary-text);
    color: white;
    border: none;
}

.reports-btn:hover {
    background-color: #555;
}

/* --- 状态标签样式 --- */
.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tag.active {
    background-color: rgba(76, 175, 80, 0.2);
    color: var(--success-color);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.tag.pending {
    background-color: rgba(255, 152, 0, 0.2);
    color: var(--warning-color);
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.tag.deleted {
    background-color: rgba(244, 67, 54, 0.2);
    color: var(--error-color);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.tag.rejected {
    background-color: rgba(156, 39, 176, 0.2);
    color: #9c27b0;
    border: 1px solid rgba(156, 39, 176, 0.3);
}

.tag.channel {
    background-color: rgba(33, 150, 243, 0.2);
    color: #2196f3;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

.tag.quality {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ff9800;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.tag.usage {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.tag.added {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
    font-weight: 600;
}

/* 已添加的视频源项样式 */
.discovery-item.added {
    border-left: 3px solid #28a745;
    background-color: rgba(40, 167, 69, 0.05);
}

.discovery-item.added .discovery-item-checkbox {
    accent-color: #28a745;
}

/* --- 统计卡片样式 --- */
.report-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    flex: 1;
    background-color: var(--tertiary-bg);
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card h4 {
    margin: 0 0 0.5rem 0;
    color: var(--secondary-text);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-card span {
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
    display: block;
}

/* --- 拖拽排序样式 --- */
.sortable-ghost {
    opacity: 0.5;
    background-color: var(--tertiary-bg);
    transform: rotate(2deg);
}

.drag-handle {
    cursor: move;
    color: var(--secondary-text);
    font-size: 1.2rem;
    padding: 0.25rem;
    border-radius: 3px;
    transition: color 0.2s ease;
}

.drag-handle:hover {
    color: var(--primary-text);
    background-color: var(--border-color);
}

/* --- 个人列表样式 --- */
.custom-name {
    background: none;
    border: none;
    color: var(--primary-text);
    font-size: 1rem;
    font-weight: 500;
    padding: 0.25rem;
    border-radius: 3px;
    transition: background-color 0.2s ease;
    width: 100%;
}

.custom-name:focus {
    outline: none;
    background-color: var(--tertiary-bg);
}

.source-actions button.favorited {
    color: #ffc107;
}

/* --- 加载和错误状态 --- */
.loading, .error, .empty {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-text);
    font-style: italic;
}

.error {
    color: var(--error-color);
}

.empty {
    color: var(--secondary-text);
}

/* --- 新的两步式流程样式 --- */

/* 步骤头部 */
.step-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.step-header h2 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-text);
    font-size: 1.5rem;
}

.step-description {
    margin: 0 0 1rem 0;
    color: var(--secondary-text);
    font-size: 0.95rem;
}

.step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
}

.step {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.step.active {
    background-color: var(--accent-color);
    color: white;
}

.step.completed {
    background-color: var(--success-color);
    color: white;
}

.step:not(.active):not(.completed) {
    background-color: var(--tertiary-bg);
    color: var(--secondary-text);
}

.step-arrow {
    color: var(--secondary-text);
    font-weight: bold;
}

/* 发现页面样式 */
.discovery-filters {
    margin-bottom: 2rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--primary-text);
    font-size: 0.95rem;
}

.quick-filters {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.filter-tag {
    padding: 0.5rem 1rem;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    color: var(--primary-text);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-tag:hover {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.filter-tag.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.precise-filters {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.precise-filters input,
.precise-filters select {
    flex: 1;
    min-width: 150px;
}

/* 发现结果样式 */
.discovery-results {
    margin-bottom: 2rem;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
}

.results-actions {
    display: flex;
    gap: 0.5rem;
}

.discovery-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--primary-bg);
}

.discovery-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.2s ease;
    cursor: pointer;
}

.discovery-item:hover {
    background-color: var(--tertiary-bg);
}

.discovery-item:last-child {
    border-bottom: none;
}

.discovery-item.selected {
    background-color: rgba(225, 6, 0, 0.05);
    border-left: 3px solid var(--accent-color);
}

.discovery-item-checkbox {
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    accent-color: var(--accent-color);
}

.discovery-item-info {
    flex: 1;
    min-width: 0;
}

.discovery-item-title {
    font-weight: 600;
    color: var(--primary-text);
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
}

.discovery-item-url {
    font-size: 0.85rem;
    color: var(--secondary-text);
    word-break: break-all;
    margin-bottom: 0.5rem;
}

.discovery-item-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.discovery-item-status {
    flex-shrink: 0;
    min-width: 100px;
    text-align: center;
}

.discovery-item-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.discovery-item-actions button {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    border-radius: 4px;
    white-space: nowrap;
}

/* 发现页面底部 */
.discovery-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.selection-summary {
    font-weight: 500;
    color: var(--primary-text);
}

.footer-actions button {
    min-width: 180px;
}

/* 个人管理页面样式 */
.management-hint {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: var(--tertiary-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.management-hint p {
    margin: 0;
    color: var(--secondary-text);
    font-size: 0.95rem;
}

.personal-content {
    min-height: 300px;
    margin-bottom: 2rem;
}

.personal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.personal-item {
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
    position: relative;
}

.personal-item:hover {
    border-color: var(--accent-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.personal-item.favorite {
    border-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.05);
}

.personal-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.personal-item-title {
    font-weight: 600;
    color: var(--primary-text);
    margin: 0;
    font-size: 1rem;
    flex: 1;
}

.personal-item-controls {
    display: flex;
    gap: 0.25rem;
    margin-left: 0.5rem;
}

.drag-handle {
    cursor: move;
    color: var(--secondary-text);
    padding: 0.25rem;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.drag-handle:hover {
    color: var(--primary-text);
    background-color: var(--border-color);
}

.favorite-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.favorite-btn:hover {
    background-color: var(--border-color);
}

.favorite-btn.favorited {
    color: #ffc107;
}

.personal-item-meta {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
}

.personal-item-url {
    font-size: 0.8rem;
    color: var(--secondary-text);
    word-break: break-all;
    margin-bottom: 0.75rem;
}

.personal-item-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--secondary-text);
}

.status-indicator.working {
    background-color: var(--success-color);
}

.status-indicator.failed {
    background-color: var(--error-color);
}

.status-indicator.testing {
    background-color: var(--warning-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.personal-item-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.personal-item-actions button {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 4px;
    white-space: nowrap;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--secondary-text);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-text);
    font-size: 1.2rem;
}

.empty-state p {
    margin: 0 0 2rem 0;
    font-size: 0.95rem;
}

/* 个人页面底部 */
.personal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 1rem;
}

.stats-summary {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stats-summary span {
    color: var(--secondary-text);
    font-size: 0.9rem;
}

.stats-summary strong {
    color: var(--accent-color);
    font-weight: 600;
}

/* --- 视频预览小窗样式 --- */
.preview-window {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 320px;
    height: 240px;
    background-color: var(--primary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    overflow: hidden;
    resize: both;
    min-width: 280px;
    min-height: 200px;
    max-width: 600px;
    max-height: 400px;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--tertiary-bg);
    border-bottom: 1px solid var(--border-color);
    cursor: move;
}

.preview-header span {
    font-weight: 500;
    color: var(--primary-text);
    font-size: 0.9rem;
}

.preview-close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--secondary-text);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.preview-close-btn:hover {
    color: var(--error-color);
    background-color: rgba(244, 67, 54, 0.1);
}

.preview-content {
    height: calc(100% - 50px);
    padding: 0;
}

.preview-content video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: #000;
}

/* --- 加载动画样式 --- */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* --- 页面加载动画样式 --- */
.page-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    color: var(--secondary-text);
}

.loading-spinner-large {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(225, 6, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
}

.loading-text {
    font-size: 1rem;
    color: var(--secondary-text);
    text-align: center;
}

.content-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem;
    color: var(--secondary-text);
    font-size: 0.95rem;
}

.content-loading .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(225, 6, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

/* 按钮内的加载动画 */
button .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
    vertical-align: middle;
}

/* --- 个人管理页面频道分组样式 --- */
.personal-channels {
    max-height: 500px;
    overflow-y: auto;
}

.channel-group {
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--primary-bg);
    overflow: hidden;
}

.channel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: var(--tertiary-bg);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.channel-header:hover {
    background-color: rgba(225, 6, 0, 0.05);
}

.channel-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: var(--primary-text);
    font-size: 1.1rem;
}

.channel-icon {
    font-size: 1.2rem;
}

.channel-count {
    background-color: var(--accent-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.channel-toggle {
    font-size: 1.2rem;
    color: var(--secondary-text);
    transition: transform 0.2s ease;
}

.channel-group.collapsed .channel-toggle {
    transform: rotate(-90deg);
}

.channel-streams {
    display: block;
    transition: all 0.3s ease;
}

.channel-group.collapsed .channel-streams {
    display: none;
}

.personal-stream-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.2s ease;
    background-color: var(--primary-bg);
}

.personal-stream-item:hover {
    background-color: var(--tertiary-bg);
}

.personal-stream-item:last-child {
    border-bottom: none;
}

.personal-stream-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    background-color: rgba(225, 6, 0, 0.1);
}

.stream-drag-handle {
    cursor: move;
    color: var(--secondary-text);
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.stream-drag-handle:hover {
    color: var(--primary-text);
    background-color: var(--border-color);
}

.personal-stream-info {
    flex: 1;
    min-width: 0;
}

.personal-stream-name {
    font-weight: 500;
    color: var(--primary-text);
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
}

.personal-stream-name input {
    background: none;
    border: none;
    color: var(--primary-text);
    font-size: 1rem;
    font-weight: 500;
    padding: 0.25rem;
    border-radius: 3px;
    transition: background-color 0.2s ease;
    width: 100%;
}

.personal-stream-name input:focus {
    outline: none;
    background-color: var(--tertiary-bg);
}

.personal-stream-url {
    font-size: 0.85rem;
    color: var(--secondary-text);
    word-break: break-all;
    margin-bottom: 0.5rem;
}

.personal-stream-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.personal-stream-status {
    flex-shrink: 0;
    min-width: 100px;
    text-align: center;
}

.personal-stream-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
    flex-wrap: wrap;
}

.personal-stream-actions button {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    border-radius: 4px;
    white-space: nowrap;
}

.favorite-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--secondary-text);
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.favorite-btn:hover {
    background-color: var(--tertiary-bg);
}

.favorite-btn.favorited {
    color: #ffc107;
    border-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.remove-btn {
    background-color: var(--error-color);
    color: white;
    border: none;
}

.remove-btn:hover {
    background-color: #d32f2f;
}
