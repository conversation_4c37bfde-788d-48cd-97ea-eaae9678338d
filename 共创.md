基于我们最新的讨论，让我重新梳理整个系统的页面、功能和逻辑。先让我了解一下当前的页面结构。

# 基于最新规划的共创视频源系统完整梳理

基于我们的最新讨论（精简的大仓库模式 + 统一弹幕室），我来重新梳理整个系统：

## 🎯 核心理念

### 系统定位
- **大仓库模式**：所有用户提交的播放源进入统一大仓库
- **个人筛选**：用户根据自己网络环境测试并挑选适合的源
- **统一弹幕**：所有用户共享同一个弹幕频道，营造社区氛围

### 权限体系（简化）
```
未登录用户：手动输入播放地址 + 查看赛程积分 + 观看弹幕
登录用户：  大仓库模式 + 提交播放源 + 测试筛选 + 发送弹幕
管理员：    删除违规播放源 + 系统配置管理
```

## 📱 页面结构与功能

### 1. 主播放页面（优化现有）

**页面组成：**
- Header：Logo + 导航 + 用户系统
- Main：视频播放器 + 播放源选择 +聊天室+ 弹幕系统
- Footer：版权信息

**功能详解：**

#### 未登录用户界面
```html
<div class="stream-selector">
    <!-- 只显示手动输入 -->
    <input type="text" id="m3u-input" placeholder="粘贴你的 M3U/M3U8 链接...">
    <button id="load-stream-btn">加载</button>
</div>
<div class="danmaku-sender">
    <!-- 弹幕输入框显示但点击时提示登录 -->
    <input type="text" id="danmaku-input" placeholder="登录后可发送弹幕...">
    <button id="send-danmaku-btn">发送</button>
</div>
```

#### 登录用户界面
```html
<div class="stream-selector">
    <!-- 显示个人播放源列表 -->
    <select id="my-streams-dropdown">
        <option value="">-- 选择我的播放源 --</option>
    </select>
    <span>或</span>
    <input type="text" id="m3u-input" placeholder="粘贴你的 M3U/M3U8 链接...">
    <button id="load-stream-btn">加载</button>
</div>
<div class="stream-management">
    <button id="contribute-btn">贡献播放源</button>
    <button id="browse-warehouse-btn">浏览大仓库</button>
    <button id="manage-my-streams-btn">管理我的播放源</button>
</div>
<div class="danmaku-sender">
    <input type="text" id="danmaku-input" placeholder="发个弹幕见证历史...">
    <button id="send-danmaku-btn">发送</button>
</div>
```

### 2. 播放源大仓库页面（新增模态框）

**访问方式：** 登录用户点击"浏览大仓库"按钮

**页面功能：**
```html
<div id="warehouse-modal" class="modal-overlay">
    <div class="modal-content">
        <button class="close-btn">&times;</button>
        <h2>播放源大仓库</h2>
        
        <!-- 搜索和筛选 -->
        <div class="warehouse-controls">
            <input type="text" id="search-streams" placeholder="搜索播放源名称...">
            <select id="sort-streams">
                <option value="newest">最新提交</option>
                <option value="name">按名称排序</option>
            </select>
            <button id="test-all-btn">批量测试</button>
        </div>
        
        <!-- 播放源列表 -->
        <div id="stream-warehouse" class="stream-list">
            <!-- 动态生成播放源项目 -->
        </div>
        
        <!-- 分页 -->
        <div class="pagination">
            <button id="prev-page">上一页</button>
            <span id="page-info">第 1 页，共 10 页</span>
            <button id="next-page">下一页</button>
        </div>
    </div>
</div>
```

**单个播放源项目：**
```html
<div class="stream-item" data-stream-id="123">
    <div class="stream-info">
        <h4>CCTV5 高清</h4>
        <p class="stream-url">https://example.com/stream.m3u8</p>
        <small class="stream-meta">
            由 <EMAIL> 提交 · 2024-01-15
        </small>
    </div>
    <div class="stream-status">
        <span class="test-result" id="test-result-123">未测试</span>
    </div>
    <div class="stream-actions">
        <button onclick="testSingleStream(123)">测试</button>
        <button onclick="previewStream(123)">预览</button>
        <button onclick="addToMyList(123)">添加到我的列表</button>
    </div>
</div>
```

### 3. 个人播放源管理页面（新增模态框）

**访问方式：** 登录用户点击"管理我的播放源"按钮

**页面功能：**
```html
<div id="my-streams-modal" class="modal-overlay">
    <div class="modal-content">
        <button class="close-btn">&times;</button>
        <h2>我的播放源</h2>
        
        <div class="my-streams-controls">
            <button id="test-my-streams">测试所有源</button>
            <button id="clear-failed-streams">清理失效源</button>
        </div>
        
        <!-- 可拖拽排序的播放源列表 -->
        <div id="my-streams-list" class="sortable-list">
            <!-- 动态生成，支持拖拽排序 -->
        </div>
    </div>
</div>
```

**个人播放源项目：**
```html
<div class="my-stream-item" data-stream-id="123" draggable="true">
    <div class="drag-handle">⋮⋮</div>
    <div class="stream-info">
        <input type="text" class="custom-name" value="CCTV5 高清" 
               onchange="updateCustomName(123, this.value)">
        <div class="stream-status">
            <span class="connection-status connected">🟢 已连接 (1.2s)</span>
        </div>
    </div>
    <div class="stream-actions">
        <button onclick="testStream(123)">测试</button>
        <button onclick="playStream(123)">播放</button>
        <button onclick="toggleFavorite(123)">⭐</button>
        <button onclick="removeFromMyList(123)">删除</button>
    </div>
</div>
```

### 4. 播放源提交页面（新增模态框）

**访问方式：** 登录用户点击"贡献播放源"按钮

**页面功能：**
```html
<div id="contribute-modal" class="modal-overlay">
    <div class="modal-content">
        <button class="close-btn">&times;</button>
        <h2>贡献播放源</h2>
        
        <form id="contribute-form">
            <div class="form-group">
                <label>播放源名称 *</label>
                <input type="text" id="stream-name" placeholder="例如：CCTV5 高清" required>
                <small>建议格式：频道名 + 画质描述</small>
            </div>
            
            <div class="form-group">
                <label>播放源地址 *</label>
                <input type="url" id="stream-url" placeholder="https://example.com/stream.m3u8" required>
                <small>支持 M3U/M3U8 格式的直播流地址</small>
            </div>
            
            <div class="form-group">
                <label>描述标签</label>
                <input type="text" id="stream-tags" placeholder="例如：1080p, 稳定, 备用线路">
                <small>可选，用逗号分隔多个标签</small>
            </div>
            
            <div class="form-actions">
                <button type="button" id="test-before-submit">测试播放源</button>
                <button type="submit">提交到大仓库</button>
            </div>
            
            <div id="test-result-display" style="display: none;">
                <!-- 显示测试结果 -->
            </div>
        </form>
    </div>
</div>
```

### 5. 管理员后台页面（扩展现有）

**访问方式：** 管理员用户在用户下拉菜单中点击"管理后台"

**新增功能模块：**
```html
<!-- 在现有系统配置基础上新增 -->
<div class="admin-section">
    <h3>播放源管理</h3>
    <div class="admin-controls">
        <button id="view-all-streams">查看所有播放源</button>
        <button id="view-deleted-streams">查看已删除播放源</button>
        <button id="export-streams">导出播放源数据</button>
    </div>
    
    <div id="admin-streams-list">
        <!-- 管理员可以看到所有播放源，包括提交者信息 -->
    </div>
</div>
```

## 🔧 核心功能逻辑

### 1. 播放源提交逻辑（极简）
```javascript
// 提交流程
用户填写表单 → 基础验证 → 直接提交到大仓库 → 立即可见

// 验证逻辑
- URL格式验证（必须是有效的HTTP/HTTPS地址）
- 重复检测（相同URL不能重复提交）
- 黑名单检测（管理员删除的URL不能重新提交）
```

### 2. 播放源测试逻辑
```javascript
// 测试流程
用户点击测试 → 创建隐藏video元素 → HLS加载测试 → 记录结果 → 更新UI

// 测试结果
- 成功：显示连接时间，标记为可用
- 失败：显示错误信息，标记为不可用
- 超时：5秒超时，标记为网络问题
```

### 3. 个人列表管理逻辑
```javascript
// 添加到个人列表
从大仓库选择播放源 → 添加到user_streams表 → 出现在个人下拉菜单

// 个人列表功能
- 自定义名称：用户可以重命名播放源
- 拖拽排序：支持手动调整播放源顺序
- 收藏标记：标记常用的播放源
- 批量测试：一键测试所有个人播放源
- 清理失效：自动移除测试失败的播放源
```

### 4. 弹幕系统逻辑（保持现有）
```javascript
// 统一弹幕室
- 所有用户共享同一个弹幕频道
- 未登录用户只能观看弹幕
- 登录用户可以发送弹幕
- 弹幕内容与具体播放源无关
- 主要讨论比赛整体情况
```

### 5. 管理员管理逻辑
```javascript
// 播放源管理
- 查看所有播放源和提交者信息
- 删除违规播放源（状态改为deleted）
- 被删除的URL加入黑名单，无法重新提交
- 查看删除历史和恢复功能
```

## 🗄️ 数据库设计（简化）

### 核心表结构
```sql
-- 播放源大仓库表
CREATE TABLE streams (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name TEXT NOT NULL,
    url TEXT NOT NULL UNIQUE,
    tags TEXT,                             -- 描述标签
    submitter_id UUID REFERENCES auth.users(id),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deleted')),
    created_at TIMESTAMP DEFAULT NOW(),
    deleted_at TIMESTAMP,
    deleted_by UUID REFERENCES auth.users(id)
);

-- 用户个人播放源列表
CREATE TABLE user_streams (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stream_id BIGINT REFERENCES streams(id) ON DELETE CASCADE,
    custom_name TEXT,                      -- 用户自定义名称
    sort_order INT DEFAULT 0,              -- 排序权重
    is_favorite BOOLEAN DEFAULT FALSE,     -- 是否收藏
    last_test_result TEXT,                 -- 最后测试结果
    last_test_time TIMESTAMP,              -- 最后测试时间
    added_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, stream_id)
);

-- 弹幕表（保持现有结构）
CREATE TABLE danmaku (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    content TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🎯 用户体验流程

### 新用户使用流程
```
1. 访问网站 → 只能手动输入播放地址
2. 注册登录 → 看到"浏览大仓库"按钮
3. 浏览大仓库 → 测试播放源 → 添加到个人列表
4. 使用个人列表 → 享受个性化播放源选择
5. 贡献播放源 → 帮助社区扩大播放源库
```

### 日常使用流程
```
1. 登录网站 → 从个人播放源下拉菜单选择
2. 播放源失效 → 切换到其他个人播放源
3. 需要新源 → 浏览大仓库 → 测试添加
4. 发现好源 → 贡献到大仓库分享给社区
```

## 💡 系统优势

1. **实现简单**：去掉复杂的审核投票逻辑，基于现有代码扩展
2. **用户友好**：每个用户根据自己网络环境筛选，个性化体验
3. **社区氛围**：统一弹幕室保持社区讨论氛围
4. **管理可控**：管理员可以删除违规内容，保持平台健康
5. **扩展性强**：后续可以轻松添加更多功能

这个方案既保持了实现的简单性，又提供了良好的用户体验，是一个平衡的解决方案。
