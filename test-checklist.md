# 视频源系统功能测试清单

## 📋 测试前准备

### 数据库准备
- [ ] 运行数据库迁移文件 `supabase/migrations/20241205_create_video_source_system.sql`
- [ ] 确认所有表已创建：streams, user_streams, stream_reports, admin_logs
- [ ] 确认RLS策略已设置
- [ ] 确认触发器已创建

### 用户准备
- [ ] 创建普通测试用户账号
- [ ] 创建管理员测试用户账号（需要在user_metadata中设置role为admin）

## 🧪 功能测试

### 1. 视频源提交功能
- [ ] 未登录用户点击"提交视频源"应提示登录
- [ ] 登录用户可以打开提交模态框
- [ ] 表单验证：必填字段检查
- [ ] 表单验证：URL格式检查
- [ ] 测试播放源功能正常工作
- [ ] 提交成功后视频源出现在大仓库中
- [ ] 重复URL提交应被拒绝

### 2. 大仓库筛选功能
- [ ] 点击"筛选视频源"打开大仓库模态框
- [ ] 搜索功能正常工作
- [ ] 频道筛选功能正常工作
- [ ] 画质筛选功能正常工作
- [ ] 排序功能正常工作
- [ ] 分页功能正常工作

### 3. 视频源测试功能
- [ ] 单个视频源测试功能
- [ ] 测试结果正确显示（成功/失败）
- [ ] 重试机制正常工作（最多3次）
- [ ] 批量测试功能正常工作

### 4. 个人列表管理
- [ ] 添加视频源到个人列表
- [ ] 个人列表按频道分组显示
- [ ] 拖拽排序功能正常工作
- [ ] 自定义名称功能正常工作
- [ ] 收藏功能正常工作
- [ ] 从个人列表删除功能正常工作

### 5. 举报功能
- [ ] 未登录用户点击举报应提示登录
- [ ] 登录用户可以打开举报模态框
- [ ] 举报原因选择功能正常
- [ ] 举报提交成功
- [ ] 同一用户同一天不能重复举报同一视频源
- [ ] 3个不同用户举报后视频源自动下架

### 6. 管理员功能
- [ ] 非管理员用户无法访问管理员功能
- [ ] 管理员可以查看所有状态的视频源
- [ ] 管理员可以通过/拒绝待审核视频源
- [ ] 管理员可以下架/上架视频源
- [ ] 管理员可以删除视频源
- [ ] 管理员可以查看举报记录
- [ ] 管理员可以查看操作日志
- [ ] 批量操作功能正常工作

### 7. 状态转换测试
- [ ] 新提交视频源状态为active
- [ ] 被举报3次后状态变为pending
- [ ] 管理员通过审核后状态变为active
- [ ] 管理员拒绝后状态变为rejected
- [ ] 管理员删除后状态变为deleted
- [ ] 各状态下的可见性正确

### 8. 统计功能
- [ ] 使用次数统计正确更新
- [ ] 最后使用时间正确更新
- [ ] 统计数据正确显示

## 🎨 UI/UX测试

### 响应式设计
- [ ] 桌面端显示正常
- [ ] 平板端显示正常
- [ ] 手机端显示正常
- [ ] 各模态框在不同屏幕尺寸下正常显示

### 交互体验
- [ ] 所有按钮点击反馈正常
- [ ] 加载状态显示正常
- [ ] 错误提示显示正常
- [ ] 成功提示显示正常
- [ ] 模态框打开/关闭动画流畅

## 🔒 安全测试

### 权限控制
- [ ] RLS策略有效阻止未授权访问
- [ ] 管理员权限检查有效
- [ ] 用户只能操作自己的数据

### 数据验证
- [ ] 前端表单验证有效
- [ ] 后端数据验证有效
- [ ] SQL注入防护有效
- [ ] XSS防护有效

## ⚡ 性能测试

### 数据加载
- [ ] 大仓库列表加载速度合理（<2秒）
- [ ] 个人列表加载速度合理（<1秒）
- [ ] 搜索响应速度合理（<1秒）
- [ ] 分页切换速度合理（<1秒）

### 批量操作
- [ ] 批量测试不会阻塞UI
- [ ] 批量添加性能合理
- [ ] 大量数据下界面仍然流畅

## 🐛 错误处理测试

### 网络错误
- [ ] 网络断开时错误提示正确
- [ ] 网络恢复后功能正常
- [ ] 超时处理正确

### 数据错误
- [ ] 无效数据处理正确
- [ ] 空数据处理正确
- [ ] 数据库错误处理正确

## 📱 兼容性测试

### 浏览器兼容性
- [ ] Chrome 最新版本
- [ ] Firefox 最新版本
- [ ] Safari 最新版本
- [ ] Edge 最新版本

### 设备兼容性
- [ ] Windows PC
- [ ] Mac
- [ ] Android 手机
- [ ] iPhone

## ✅ 测试完成标准

所有测试项目通过后，系统即可投入使用。如发现问题，需要修复后重新测试相关功能。

## 📝 测试记录

测试日期：_______
测试人员：_______
测试环境：_______
测试结果：_______

发现的问题：
1. _______
2. _______
3. _______

修复情况：
1. _______
2. _______
3. _______
